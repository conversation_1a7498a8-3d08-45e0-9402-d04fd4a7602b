'use strict';

// Helper to format dates
function getSydneyDates() {
  const tz = 'Australia/Sydney';
  const dtf = new Intl.DateTimeFormat('en-CA', {
    timeZone: tz,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });

  // Derive "today" parts in the target timezone
  const parts = Object.fromEntries(dtf.formatToParts(new Date()).map((p) => [p.type, p.value]));
  const y = Number(parts.year);
  const m = Number(parts.month);
  const d = Number(parts.day);

  // Anchor to UTC midnight for that local date, then add 1 day for tomorrow
  const todayUtc = new Date(Date.UTC(y, m - 1, d));
  const tomorrowUtc = new Date(todayUtc);
  tomorrowUtc.setUTCDate(todayUtc.getUTCDate() + 1);

  return { today: dtf.format(todayUtc), tomorrow: dtf.format(tomorrowUtc) };
}

const { today, tomorrow } = getSydneyDates();

const checkoutURL = `https://staging-hotels-qantas-akamai.jqdev.net/hotels/checkout?adults=2&checkIn=${today}&checkOut=${tomorrow}&children=0&exclusiveOffer=false&infants=0&offerId=3520737&payWith=cash&propertyId=1135511&roomTypeId=7216693`;

module.exports = {
  defaults: {
    standard: 'WCAG2AA',
    runners: ['axe', 'htmlcs'],
    hideElements: ['iframe[src*="doubleclick.net"]', 'textarea.g-recaptcha-response', '#Layer_1', '#layer2_19_', '#layer1_44_'],
    chromeLaunchConfig: {
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
      ],
    },
    useIncognitoBrowserContext: true,
  },
  urls: [
    'https://staging-hotels-qantas-akamai.jqdev.net/hotels',
    'https://staging-hotels-qantas-akamai.jqdev.net/hotels/search/list',
    'https://staging-hotels-qantas-akamai.jqdev.net/hotels/search/map',
    'https://staging-hotels-qantas-akamai.jqdev.net/hotels/campaigns/cypress-campaign',
    'https://staging-hotels-qantas-akamai.jqdev.net/hotels/deals',
    'https://staging-hotels-qantas-akamai.jqdev.net/hotels/contact-us',
    'https://staging-hotels-qantas-akamai.jqdev.net/hotels/faqs',
    'https://staging-hotels-qantas-akamai.jqdev.net/hotels/terms-and-conditions',
    'https://staging-hotels-qantas-akamai.jqdev.net/hotels/airbnb',
    'https://staging-hotels-qantas-akamai.jqdev.net/hotels/404',
    'https://staging-hotels-qantas-akamai.jqdev.net/hotels/properties/1135511?adults=2',
    checkoutURL,
  ],
};
