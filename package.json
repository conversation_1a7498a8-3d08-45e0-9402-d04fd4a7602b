{"name": "qantas-hotels-ui", "version": "0.1.0", "private": true, "scripts": {"start": "node src/server.js", "dev": "yarn start", "build": "next build", "build:dev": "env-cmd -f .env.development yarn run build", "test:browser": "NODE_ENV=test NODE_OPTIONS='--no-deprecation' jest --config jest/jest.config.browser.ts --coverage", "test:browser:shard": "NODE_OPTIONS='--no-deprecation' ./scripts/dev/run-sharded-browser-unit-tests.sh", "test:browser:watch": "NODE_ENV=test NODE_OPTIONS='--no-deprecation' jest --config jest/jest.config.browser.ts --watch", "test:browser:fast": "NODE_ENV=test NODE_OPTIONS='--no-deprecation' jest --config jest/jest.config.browser.ts -o --passWithNoTests", "test:server": "NODE_ENV=test jest --config jest/jest.config.server.ts --coverage", "test:server:watch": "NODE_ENV=test jest --config jest/jest.config.server.ts --watch", "test:server:fast": "NODE_ENV=test jest --config jest/jest.config.server.ts -o --passWithNoTests", "test:e2e": "env-cmd -f .env.e2e cypress run --browser chrome", "test:e2e:parallel": "cypress-parallel -s test:e2e", "test:e2e:dev": "env-cmd -f .env.e2e cypress open --browser chrome -c baseUrl=http://localhost:3000", "test:a11y": "pa11y-ci --config .pa11yci.js", "serve": "NODE_ENV=production node --max-http-header-size=32000 src/server.js --config tsconfig.server.json", "lint": "eslint .", "quickLint": "eslint . --fix --quiet", "precommit": "lint-staged", "prepare": "husky"}, "browserslist": {"development": ["last 2 chrome versions", "last 2 firefox versions", "last 2 edge versions"], "production": [">1%", "last 2 versions", "Firefox ESR", "not ie 11", "not dead"]}, "dependenciesComments": {"jest": "Jest is pinned to version 29.7.0 to resolve ESM import errors with coverageThreshold and CoverageReporter in Jest 30. We will upgrade to Jest 31 once it reaches stable release, and if it fixes this issue."}, "dependencies": {"@emotion/core": "^10.0.17", "@emotion/styled": "^10.0.17", "@loadable/component": "^5.16.7", "@loadable/server": "^5.16.7", "@optimizely/react-sdk": "^3.2.4", "@popperjs/core": "^2.11.8", "@qantasexperiences/analytics": "^1.47.0", "@qantasexperiences/analytics-old": "npm:@qantasexperiences/analytics@0.20.0", "@qga/components": "^2.4.0", "@qga/roo-ui": "^5.20.1", "@qga/sanity-components": "^1.2.3", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-navigation-menu": "1.1.4", "@reach/accordion": "0.18.0", "@reach/auto-id": "0.18.0", "@reach/disclosure": "0.18.0", "@reach/dropdown": "0.18.0", "@reach/menu-button": "0.18.0", "@reach/popover": "0.18.0", "@reach/tabs": "0.18.0", "@redux-beacon/combine-events": "^1.0.0", "@redux-beacon/google-tag-manager": "^1.0.1", "@reduxjs/toolkit": "^1.9.5", "@sanity/block-content-to-markdown": "^0.0.6", "@sanity/client": "^3.4.1", "@sentry/nextjs": "8.53.0", "@u-wave/react-vimeo": "^0.9.12", "@u-wave/react-youtube": "^0.7.4", "adyen-web-v5": "npm:@adyen/adyen-web@5.69.1", "adyen-web-v6": "npm:@adyen/adyen-web@6.12.0", "axios": "^1.12.2", "body-parser": "^1.20.3", "body-scroll-lock": "^3.1.5", "bowser": "^2.12.1", "connected-next-router": "^5.0.0", "connected-react-router": "^6.9.3", "cookie-parser": "^1.4.7", "cookies": "^0.9.1", "copy-to-clipboard": "^3.3.3", "crypto": "^1.0.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "dayzed": "^3.2.3", "decimal.js": "^10.6.0", "downshift": "^9.0.10", "emotion-theming": "^10.0.27", "env-cmd": "^10.1.0", "esbuild": "^0.25.9", "express": "^4.21.2", "fecha": "^4.2.3", "focus-trap-react": "^10.3.0", "get-youtube-id": "^1.0.1", "google-map-react": "2.2.5", "history": "^5.3.0", "html-react-parser": "^5.2.6", "ignore-styles": "^5.0.1", "intersection-observer": "^0.12.2", "isbot": "^3.6.13", "js-cookie": "^3.0.5", "js-sha256": "^0.11.1", "lodash": "^4.17.21", "markdown-to-jsx": "7.5.0", "msgpack-lite": "^0.1.26", "mutationobserver-shim": "^0.3.3", "next": "12.3.6", "next-compose-plugins": "^2.2.1", "next-images": "^1.8.5", "next-redux-wrapper": "^8.1.0", "next-transpile-modules": "^9.0.0", "normalize.css": "^8.0.1", "pino": "^6.14.0", "pino-http": "^6.6.0", "pluralize": "^8.0.0", "polished": "^4.3.1", "prop-types": "^15.7.2", "query-string": "^7.1.1", "ramda": "^0.31.3", "rc-slider": "^9.7.5", "react": "^17.0.2", "react-dom": "^17.0.2", "react-focus-within": "^2.0.2", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.62.0", "react-hook-form-persist": "^3.0.0", "react-intersection-observer": "^9.16.0", "react-popper": "^2.3.0", "react-redux": "^7.1.1", "react-responsive": "^9.0.2", "react-router": "^6.26.2", "react-router-dom": "^6.26.2", "react-transition-group": "^4.4.5", "react-use": "^17.6.0", "react-useportal": "^1.0.19", "redux-beacon": "^2.1.0", "redux-logic": "^3.0.3", "redux-logic-test": "^2.0.0", "reselect": "^4.1.8", "rxjs": "^7.8.2", "source-map-support": "^0.5.13", "styled-system": "^4.2.4", "universal-cookie": "^8.0.0", "uuid": "^9.0.1", "visibilityjs": "^2.0.2", "wicg-inert": "^3.1.3", "zod": "^3.25.71"}, "devDependencies": {"@axe-core/react": "^4.10.2", "@babel/core": "^7.28.3", "@babel/eslint-parser": "^7.28.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.28.3", "@babel/preset-env": "^7.28.3", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@bahmutov/cypress-esbuild-preprocessor": "^2.2.4", "@cypress/webpack-preprocessor": "^7.0.0", "@loadable/webpack-plugin": "^5.15.2", "@next/bundle-analyzer": "~12.1", "@qga/pino-pretty-logger": "^1.0.0", "@simonsmith/cypress-image-snapshot": "^10.0.2", "@testing-library/cypress": "10.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "12.1.5", "@testing-library/react-hooks": "^8.0.0", "@testing-library/user-event": "^14.6.1", "@types/lodash": "^4.17.20", "@types/node": "^22.15.30", "@types/react-redux": "^7.1.25", "@types/styled-system": "^4.2.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "ansi-colors": "^4.1.3", "assets-webpack-plugin": "^7.1.1", "babel-jest": "29.7.0", "babel-loader": "^9.2.1", "babel-plugin-dynamic-import-node": "^2.3.3", "bundle-stats-webpack-plugin": "^4.21.2", "copy-webpack-plugin": "^13.0.1", "css-loader": "^7.1.2", "cypress": "^13.17.0", "cypress-fail-fast": "^7.1.1", "cypress-iframe": "^1.0.1", "cypress-image-diff-js": "^2.4.0", "cypress-multi-reporters": "^2.0.5", "cypress-parallel": "^0.15.0", "dotenv": "^16.5.0", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-cypress": "^4.3.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-mocha": "10.5.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-testing-library": "^7.6.6", "esm": "^3.2.25", "file-loader": "^6.2.0", "husky": "^9.1.7", "jest": "29.7.0", "jest-canvas-mock": "^2.5.1", "jest-emotion": "^10.0.32", "jest-environment-jsdom": "29.7.0", "jest-fetch-mock": "^3.0.3", "jest-mock-random": "^1.1.1", "jest-watch-typeahead": "^3.0.1", "jest-when": "^3.7.0", "lint-staged": "^16.1.5", "mini-css-extract-plugin": "^2.9.4", "mockdate": "^3.0.5", "next-router-mock": "^1.0.2", "pa11y-ci": "^4.0.1", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "postcss-preset-env": "^10.2.4", "prettier": "^3.6.2", "react-is": "^17.0.2", "react-remock": "^0.8.2", "react-test-renderer": "^17.0.2", "redux-mock-store": "^1.5.5", "rimraf": "^6.0.1", "start-server-webpack-plugin": "^2.2.5", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typescript": "^5.9.2", "webpack": "^5.101.2"}, "resolutions": {"**/@types/node": "^22.15.30", "next/@types/react": "17.0.30", "**/@typescript-eslint/utils": "6.21.0", "ts-node": "^10.9.2", "typescript": "^5.8.2", "listr2": "6.6.1", "string-width": "4.2.3", "strip-ansi": "6.0.1", "wrap-ansi": "7.0.0", "lru-cache": "5.1.1"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}