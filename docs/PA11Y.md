# Pa11y Accessibility Testing

We use [pa11y-ci](https://github.com/pa11y/pa11y-ci) for automated accessibility testing in the CI/CD pipeline.

## Configuration

The pa11y configuration is defined in `.pa11yci.js` and includes:

- **Standard**: WCAG 2.1 AA compliance testing
- **Runners**: Both Axe and HTML Code Sniffer for comprehensive coverage
- **URLs**: Key application pages including home, search, campaigns, and static pages

## Running Tests

### Local Development

```bash
# Run all accessibility tests (console output)
yarn test:a11y

# Run with custom threshold (allows up to N accessibility issues)
yarn test:a11y --threshold 10
```

### CI/CD Pipeline

Pa11y tests run automatically in the Buildkite pipeline after staging deployment. The tests run against the staging environment URLs.

## Test Results

- **Console Output**: Shows detailed accessibility violations in the terminal
- **Thresholds**: Builds fail if more than the configured number of issues are found

## Configuration Options

Key settings in `.pa11yci.js`:

- `standard`: WCAG2AA for Level AA compliance
- `runners`: ["axe", "htmlcs"] for dual-engine testing
- `threshold`: Set via command line (default: 0)

## Troubleshooting

If tests fail:

1. Check the console output for specific accessibility violations
2. Adjust thresholds temporarily if needed for urgent releases
3. Fix underlying accessibility issues for long-term compliance
