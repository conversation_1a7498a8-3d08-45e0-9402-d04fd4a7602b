import { testId } from '../../support/helpers';
import { defaultLocation, circleTooltipRegex, starRatingTooltipRegex } from '../../support/commonParams';
import { ratingFilterTestSuites, visualRatingTestSuites } from '../../support/searchTestSuites';

ratingFilterTestSuites.forEach((suiteConfig) => {
  const { suiteName, filterQueryName, filterId, beforeAction } = suiteConfig;
  const QUERY_KEY = `min${filterQueryName}Rating`;
  const RATING_FILTER_SELECTOR = `${testId(filterId)} input[type="radio"][name="${QUERY_KEY}"]`;
  const RATING_DESCRIPTION_TESTID = filterQueryName === 'Star' ? 'hotel-rating-description' : 'tripadvisor-rating-description';
  const RESULT_COUNT_TESTID = suiteName.includes('list') ? 'result-count' : 'total-results';

  describe(`${suiteName}`, { testIsolation: false }, () => {
    before(() => {
      beforeAction();
    });

    it('defaults to all', () => {
      cy.get(RATING_FILTER_SELECTOR).filter(':checked').should('have.value', '');
      cy.assertQueryParam((params) => expect(params).to.not.have.property(QUERY_KEY));
    });

    ['3', '4', '5'].forEach((rating) => {
      describe(`when rating ${rating}`, () => {
        before(() => {
          cy.clickAndWaitRatingFilter(RATING_FILTER_SELECTOR, rating);
        });

        it('updates the search', () => {
          cy.assertRoutedToQueryParam(QUERY_KEY, rating);
          cy.get(RATING_FILTER_SELECTOR).filter(':checked').should('have.value', rating);
        });

        it(`shows only results with a rating of ${rating} or higher`, () => {
          cy.checkPageRatings(rating, RATING_DESCRIPTION_TESTID, suiteName, RESULT_COUNT_TESTID);
        });
      });
    });
  });
});

describe('Visual Ratings', () => {
  visualRatingTestSuites.forEach((suiteConfig) => {
    const { view, platform, container, isMobile, isMapsView } = suiteConfig;

    describe(`${view} view - ${platform} Ratings tooltip`, () => {
      beforeEach(() => {
        if (view === 'list') {
          cy.visitEmptySearch();
        } else if (view === 'map') {
          cy.visitEmptySearch(defaultLocation, 'map');
        }
      });

      it('should assert visibility of the circle rating tooltip', () => {
        cy.checkTooltipVisibility({
          ratingType: 'rating-circle',
          tooltipRegex: circleTooltipRegex,
          container,
          isMobile,
          isMapsView,
        });
      });

      it('should assert visibility of the star rating tooltip', () => {
        cy.checkTooltipVisibility({
          ratingType: 'rating-star',
          tooltipRegex: starRatingTooltipRegex,
          container,
          isMobile,
          isMapsView,
        });
      });
    });
  });
});
