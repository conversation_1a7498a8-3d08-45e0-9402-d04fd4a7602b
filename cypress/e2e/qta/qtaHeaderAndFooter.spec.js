import { QTA_SCREENSIZES } from 'config';
import { testId } from '../../support/selectors';

QTA_SCREENSIZES.forEach((screenSize) => {
  describe(`QTA tests for screen size: ${screenSize}`, () => {
    describe('Header ', () => {
      it(`mobile header menu button should not be present for ${screenSize}`, () => {
        cy.changeViewPort(screenSize);
        cy.visitQtaSearchWithStayAttributes();
        cy.findByTestId('menu-button').should('not.exist');
      });
    });

    describe(`Footer`, { testIsolation: false }, () => {
      before(() => {
        cy.visitQtaPropertyWithStayAttributes();
      });

      beforeEach(() => {
        cy.changeViewPort(screenSize);
      });

      it('should render with the correct links and text', () => {
        cy.get(testId('footer-logo')).should('exist');
        cy.get(testId('scroll-to-top')).should('exist');
        cy.get(testId('qta-manage-bookings-link')).should('include.text', 'Manage Hotels Bookings');
        cy.get(testId('qta-faqs-link')).should('include.text', 'FAQs');
        cy.get(testId('qta-contact-link')).should('include.text', 'Contact Us');
        cy.get(testId('acknowledgement-text')).should(
          'include.text',
          'Qantas would like to acknowledge the Traditional Custodians of the local lands and waterways on which we live, work and fly.We pay our respects to Elders past and present.',
        );
        cy.get(testId('abn')).should('include.text', 'Qantas Airways Limited ABN **************');

        cy.get(testId('footer-links-header')).should('not.exist');
        cy.get(testId('destinations-mobile-link')).should('not.exist');
        cy.get(testId('deals-link')).should('not.exist');
        cy.get(testId('airbnb-link')).should('not.exist');
        cy.get(testId('luxe-link')).should('not.exist');
        cy.get(testId('melbourne-destinations-link')).should('not.exist');
        cy.get(testId('surfers-paradise-destinations-link')).should('not.exist');
      });

      it('should render the Privacy & Security link with the correct attributes', () => {
        cy.get(testId('privacy-security-link'))
          .should('include.text', 'Privacy & Security')
          .and('have.attr', 'href', 'https://www.qantas.com/au/en/support/privacy-and-security.html')
          .and('have.attr', 'target', '_blank')
          .and('have.attr', 'rel', 'noopener noreferrer');
      });

      it('should render the Terms of use link with the correct attributes', () => {
        cy.get(testId('term-of-use-link'))
          .should('include.text', 'Terms of use')
          .and('have.attr', 'href', 'https://www.qantas.com/au/en/support/terms-of-use.html')
          .and('have.attr', 'target', '_blank')
          .and('have.attr', 'rel', 'noopener noreferrer');
      });
    });
  });
});
