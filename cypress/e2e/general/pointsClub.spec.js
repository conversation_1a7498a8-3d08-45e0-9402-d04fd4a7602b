import { submitPersonalDetailsForm } from '../../support/common';
import { HOTELS_PATH } from 'config/env';

const textToFloat = (text) => parseFloat(text.replace(/([^\d.])/g, ''));

const { findByTestId, findAllByTestId, findByLabelText, findByText } = cy;

describe('Points club', () => {
  describe('Search Page', () => {
    beforeEach(() => {
      cy.visitEmptySearch();
      cy.intercept(`${HOTELS_PATH}/api/ui/locations/*/availability?**`).as('search-results');
    });

    it('can login and see increased points club values', () => {
      let initialPointsAmount;

      findAllByTestId('search-result')
        .first()
        .within(() => {
          const $pointsDisplay = Cypress.$('[data-testid="total-points-displayed"]');

          if ($pointsDisplay.length > 0) {
            cy.wrap($pointsDisplay)
              .first()
              .invoke('text')
              .then((totalQantasPoints) => {
                initialPointsAmount = textToFloat(totalQantasPoints);
              });
          }
        });

      cy.loginViaSSOPointsClubPlusMember();

      cy.wait('@search-results');

      findAllByTestId('search-result')
        .first()
        .within(() => {
          const $pointsDisplay = Cypress.$('[data-testid="total-points-displayed"]');

          if ($pointsDisplay.length > 0) {
            cy.wrap($pointsDisplay)
              .first()
              .invoke('text')
              .then((totalQantasPoints) => {
                expect(textToFloat(totalQantasPoints)).to.be.greaterThan(initialPointsAmount);
              });
          }
        });
    });
  });

  describe('Checkout Page', () => {
    beforeEach(() => {
      cy.visitCheckoutWithStayAttributes();
    });

    it('can login and see increased points club values and bonus points text', () => {
      let initialPointsAmount;

      submitPersonalDetailsForm();

      findByTestId('qff-and-abn-points').within(() => {
        findAllByTestId('total-points-displayed')
          .first()
          .invoke('text')
          .then((totalQantasPoints) => {
            initialPointsAmount = textToFloat(totalQantasPoints);
          });
      });

      findByTestId('qff-and-abn-points').within(() => {
        findByTestId('points-club-earn-bonus-text').should('not.exist');
        findByTestId('points-club-modal-link').should('not.exist');
        findByTestId('points-club-logo').should('not.exist');
      });

      cy.loginViaSSOPointsClubPlusMember();

      submitPersonalDetailsForm();

      findByTestId('qff-and-abn-points').within(() => {
        findAllByTestId('total-points-displayed')
          .first()
          .invoke('text')
          .then((totalQantasPoints) => {
            expect(textToFloat(totalQantasPoints)).to.be.greaterThan(initialPointsAmount);
          });
      });

      findByTestId('qff-and-abn-points').within(() => {
        findByTestId('points-club-earn-bonus-text').should('exist');
        findByTestId('points-club-modal-link').should('exist');
        findByTestId('points-club-logo').should('exist');
      });
    });

    it('warns if the QFF number is changed', () => {
      submitPersonalDetailsForm();

      cy.loginViaSSOPointsClubPlusMember();

      findByText(/Available Qantas Points:/i).should('be.visible');

      submitPersonalDetailsForm();

      findByTestId('qantas-points-form').within(() => {
        findByTestId('qff-points-club-notice').should('not.exist');

        findByLabelText(/Qantas Frequent Flyer number/i).type('1234567890');
        findByLabelText(/ABN for business trips/i).click();

        findByTestId('qff-points-club-notice').should('exist');

        findByLabelText(/Qantas Frequent Flyer number/i).clear();
        findByLabelText(/Qantas Frequent Flyer number/i).type('1989050586');
        findByTestId('qff-points-club-notice').should('not.exist');
      });
    });
  });
});
