import { HOTELS_PATH } from 'config';
import { testId } from '../../support/selectors';

describe('Contact us page', { testIsolation: false }, () => {
  before(() => {
    cy.visit({ url: `${HOTELS_PATH}/contact-us`, retryOnStatusCodeFailure: true, retryOnNetworkFailure: true });
  });

  it('recommended links section should match the saved snapshots', () => {
    const imagesInRecommendedSection = '[data-testid="recommended-links-section"] [data-testid="image"]';
    cy.get(imagesInRecommendedSection).should('have.length', 7).and('be.visible');
    cy.get(testId('recommended-links-section')).matchImageSnapshot('recommended-links-section', { failureThreshold: 0.01 });
  });

  it('faqs links block should match the saved snapshots', () => {
    const chevronsInFaqSection = '[data-testid="faqs-links-block"] [title="chevronRight"]';
    cy.get(chevronsInFaqSection).should('have.length', 7).and('be.visible');
    cy.get(testId('faqs-links-block')).matchImageSnapshot('faqs-links-section', { failureThreshold: 0.01 });
  });
});

describe('faqs page', { testIsolation: false }, () => {
  before(() => {
    cy.visit({ url: `${HOTELS_PATH}/faqs`, retryOnStatusCodeFailure: true, retryOnNetworkFailure: true });
  });

  it('has the title', () => {
    cy.findByText(/Frequently Asked Questions/i).should('exist');
  });

  describe('Q&As links block', () => {
    it('has the title', () => {
      cy.findByText(/Popular topics/i).should('exist');
    });

    it('has the "Booking Questions" link', () => {
      cy.get(testId('booking-questions')).should('exist');
    });

    it('has the "Qantas Frequent Flyer Customers" link', () => {
      cy.get(testId('qantas-frequent-flyer-customers')).should('exist');
    });

    it('has the "Payments and Changes" link', () => {
      cy.get(testId('payments')).should('exist');
    });

    it('has the "Changes and cancellations" link', () => {
      cy.get(testId('changes-and-cancellations')).should('exist');
    });

    it('has the "General Enquiries and Feedback" link', () => {
      cy.get(testId('general-enquiries-and-feedback')).should('exist');
    });

    it('has the "Vouchers" link', () => {
      cy.get(testId('vouchers')).should('exist');
    });
  });

  describe('questions blocks', () => {
    it('has the "Booking Questions" block', () => {
      cy.get('#booking-questions').should('exist');
    });

    it('has the "Qantas Frequent Flyer Customers" question block', () => {
      cy.get('#qantas-frequent-flyer-customers').should('exist');
    });

    it('has the "Payments and Changes" question block', () => {
      cy.get('#payments').should('exist');
    });

    it('has the "Changes and cancellations" question block', () => {
      cy.get('#changes-and-cancellations').should('exist');
    });

    it('has the "General Enquires and Feedback" question block', () => {
      cy.get('#general-enquiries-and-feedback').should('exist');
    });

    it('has the "Vouchers" question block', () => {
      cy.get('#vouchers').should('exist');
    });
  });

  describe('question and answers', () => {
    it('initially displays collapsed content', () => {
      cy.get(testId('faqs-answer-block')).should('not.be.visible');
    });

    it('clicks the first question accordion and displays the answer', () => {
      cy.get(testId('question-answer-block')).first().click();
      cy.get(testId('faqs-answer-block')).first().should('be.visible');
    });
  });
});

describe('Deals hub', { testIsolation: false }, () => {
  before(() => {
    cy.intercept('GET', '/hotels/api/ui/locations/deals*', { fixture: 'dealsResponse.json' }).as('getDeals');
    cy.intercept('GET', '/hotels/api/ui/campaigns', { fixture: 'campaignsResponse.json' }).as('getCampaigns');
    cy.visitDealsHub();
  });

  it('receives expected API responses', () => {
    cy.wait('@getDeals').its('response.statusCode').should('eq', 200);
    cy.wait('@getCampaigns').its('response.statusCode').should('eq', 200);
  });

  it('renders the header heading', () => {
    cy.get(testId('deals-header-heading')).should('exist');
  });

  it('renders the modal button', () => {
    cy.get(testId('open-modal-button')).should('exist');
  });

  it('renders the deals', () => {
    const dealTypes = [
      { id: 'all_deals', text: 'All deals' },
      { id: 'best_deals', text: 'Best deals' },
      { id: 'bonus_points', text: 'Bonus Points' },
      { id: 'classic_rewards', text: 'Classic Rewards' },
      { id: 'luxury_offers', text: 'Luxury offers' },
    ];

    dealTypes.forEach(({ id, text }) => {
      cy.get('body').then(($body) => {
        if ($body.find(`[data-testid="filter-option-${id}"]`).length > 0) {
          cy.get(testId(`filter-option-${id}`))
            .should('be.visible')
            .should('have.text', text);
        }
      });
    });
  });

  it('renders the heading for best deals', () => {
    cy.get(testId('deals-type-heading')).first().should('exist');
  });

  it('validates Destinations Modal on mobile', () => {
    cy.viewport('iphone-x');
    const destinations = [
      'Select a destination',
      'Queensland',
      'New South Wales',
      'Victoria',
      'Western Australia',
      'South Australia',
      'Tasmania',
    ];
    const validateDestinations = () => {
      cy.get(testId('mobile-nav-trigger-button')).click();
      destinations.forEach((destination) => {
        cy.scrollToDestination(destination);
      });
      cy.get(testId('close-modal-button')).first().click();
    };
    validateDestinations();
  });

  describe('renders the deals card', () => {
    it('renders deals card', () => {
      cy.get(testId('stacked-card')).first().should('exist');
    });

    it('renders the link for deals card', () => {
      cy.get(testId('stacked-card'))
        .first()
        .should('have.attr', 'href')
        .and('match', /hotels\/properties/);
    });

    it('renders the Australian destinations', () => {
      cy.get(testId('list-regions-title')).should('exist');
      cy.get(testId('list-regions-cta')).should('have.attr', 'href', '/hotels/australia');
    });

    it('renders the discover destinations', () => {
      cy.findByTestId('popular-destination-header').should('not.be.empty');
      const regionLinks = '[data-testid="popular-destination-footer"] [data-testid="region-links-header"]';
      cy.get(regionLinks).should('have.length.at.least', 4);
      cy.get(regionLinks).eq(0).should('not.be.empty');
      cy.get(regionLinks).eq(1).should('not.be.empty');
      cy.get(regionLinks).eq(2).should('not.be.empty');
      cy.get(regionLinks).eq(3).should('not.be.empty');
    });
  });

  describe('Star Rating and Circle-Rating tooltips - Desktop', () => {
    it('should hover over Circle-Rating and assert visibility of the tooltip', () => {
      cy.checkTooltipVisibility({
        ratingType: 'rating-circle',
        tooltipRegex: /This is a self assigned rating by the service provider or obtained through Expedia\./,
      });
    });
    it('should hover over Star-Rating element and assert visibility of the tooltip', () => {
      cy.checkTooltipVisibility({
        ratingType: 'rating-star',
        tooltipRegex: /This official star rating is provided by the property, verified by an independent third party\./,
      });
    });
  });

  describe('Star Rating and Circle-Rating tooltips - Mobile', { scrollBehavior: 'center' }, () => {
    it('should click on Circle-Rating and assert visibility of the tooltip', () => {
      cy.checkTooltipVisibility({
        ratingType: 'rating-circle',
        tooltipRegex: /This is a self assigned rating by the service provider or obtained through Expedia\./,
        isMobile: true,
      });
    });
    it('should click on Star-Rating element and assert visibility of the tooltip', { scrollBehavior: 'center' }, () => {
      cy.checkTooltipVisibility({
        ratingType: 'rating-star',
        tooltipRegex: /This official star rating is provided by the property, verified by an independent third party\./,
        isMobile: true,
      });
    });
  });
});
