import { testId } from '../../support/helpers';
import { addDays, startOfDay } from 'date-fns';

const LOCATION_INPUT = 'location-search-input';
const AIRBNB_HOST = 'https://www.airbnb.com';
const AIRBNB_PATH = '/s/Hobart,TAS,Australia/homes';
const newSearchText = 'hobart';
const newLocation = 'Hobart, TAS, Australia';

const { get, findByTestId, findAllByTestId, viewport } = cy;
const checkInDate = startOfDay(addDays(Date.now(), 10));
const checkOutDate = startOfDay(addDays(Date.now(), 20));

describe('Homepage search', () => {
  beforeEach(() => {
    cy.visitHomepage();
  });

  it('navigates to the search results page', () => {
    cy.setupWaitSearchResults();

    cy.searchForLocation(newSearchText, 5, newLocation);
    cy.findAllByTestId(LOCATION_INPUT).first().should('have.attr', 'placeholder', newLocation);

    cy.selectDatesWithMouse(checkInDate, checkOutDate);
    cy.addChildren();

    cy.findByTestId('search-hotels-cta').invoke('removeAttr', 'target').click();
    cy.waitSearchResults();

    cy.assertRoutedToQueryParam('location', newLocation);
    cy.assertLocationUpdated(newSearchText);

    cy.assertCommonRouteToQueryParams();
  });

  it('navigates to the property page', () => {
    const propertySearchText = 'hilton-sydney';
    const propertyLocation = 'Hilton Sydney, Sydney, Australia';

    cy.setupWaitPropertyPage();

    cy.searchForLocation(propertySearchText, 1, propertyLocation);
    cy.findAllByTestId(LOCATION_INPUT).first().should('have.attr', 'placeholder', propertyLocation);

    cy.selectDatesWithMouse(checkInDate, checkOutDate);
    cy.addChildren();

    cy.findByTestId('search-hotels-cta').click();
    cy.waitPropertyPage();

    cy.location().should((location) => {
      expect(location.pathname).to.eq('/hotels/properties/109435');
    });

    cy.assertCommonRouteToQueryParams();
  });

  it('navigates to the airbnb page', () => {
    cy.findByTestId('airbnb-tab').click();

    cy.searchForAirbnbLocation(newSearchText, 5, newLocation);
    cy.findAllByTestId(LOCATION_INPUT).last().should('have.attr', 'placeholder', newLocation);

    cy.selectDatesWithMouse(checkInDate, checkOutDate, 'last');
    cy.addChildren('airbnb');

    cy.findByTestId('search-airbnb-cta').click();

    cy.enterLoginDetails();

    cy.findByTestId('search-airbnb-cta').click();

    // eslint-disable-next-line no-unused-vars
    Cypress.on('uncaught:exception', (err, runnable) => {
      return false;
    });

    cy.location().should((loc) => {
      expect(loc.pathname).to.include(AIRBNB_PATH);
      expect(loc.host).to.eq(new URL(AIRBNB_HOST).host);
    });
    cy.go('back');
  });
});

describe('Homepage content', { testIsolation: false }, () => {
  before(() => {
    cy.visitHomepage();
  });

  it('renders the simplicity menu', () => {
    findAllByTestId('qantas-universal-nav-wrapper').first().should('exist');
  });

  it('renders the navigation menu', () => {
    cy.assertNavigationMenuItems();
  });

  it('renders the carousel of featured campaigns', () => {
    cy.checkFeaturedCampaignsCarousel();
  });

  it('renders the list of regions', () => {
    findByTestId('list-regions-title').should('exist');
    findAllByTestId('region-link').should('have.length.at.least', 4);
    findByTestId('list-regions-cta').should('exist');
  });

  it('renders the value propositions', () => {
    findAllByTestId('slimline-layout').should('have.length.at.least', 2);
    get('[data-testid="slimline-layout"]').eq(0).should('exist');
    get('[data-testid="slimline-layout"]').eq(1).should('exist');
  });

  it('renders the cross sell tiles', () => {
    findAllByTestId('cross-sell-tile').should('have.length.at.least', 2);
    get('[data-testid="cross-sell-tile-title"]').eq(0).should('not.be.empty');
    get('[data-testid="cross-sell-tile-title"]').eq(1).should('not.be.empty');
  });

  it('renders the offers list', () => {
    findAllByTestId('offer-link').should('have.length.at.least', 2);
    get('[data-testid="offer-link"]').eq(0).should('have.attr', 'href');
    get('[data-testid="offer-link"]').eq(1).should('have.attr', 'href');
  });

  it('renders the destination links', () => {
    findByTestId('popular-destination-header').should('not.be.empty');
    const regionLinks = '[data-testid="popular-destination-footer"] [data-testid="region-links-header"]';
    get(regionLinks).should('have.length.at.least', 4);
    get(regionLinks).eq(0).should('not.be.empty');
    get(regionLinks).eq(1).should('not.be.empty');
    get(regionLinks).eq(2).should('not.be.empty');
    get(regionLinks).eq(3).should('not.be.empty');
  });
});

describe('Homepage - mobile', { testIsolation: false }, () => {
  before(() => {
    cy.visitHomepage();
    viewport('iphone-x');
    findByTestId('menu-button').click();
  });

  it('renders the simplicity menu', () => {
    get(`${testId('phone-menus')} ${testId('qantas-universal-nav-wrapper')}`)
      .first()
      .should('exist');
  });

  it('renders the navigation menu', () => {
    cy.assertPhoneNavigationMenuItems();
  });
});
