import { getDefaults } from '../../src/lib/enums/search';
import { PAYMENT_METHODS } from '../../src/lib/enums/payment';

export const QUERY_DATE_FORMAT = 'yyyy-MM-dd';

const { adults, checkIn, checkOut, children, infants, sortBy } = getDefaults();

export const defaultCheckIn = checkIn;
export const defaultCheckOut = checkOut;
export const defaultLocation = 'Melbourne, VIC, Australia';
export const hobartSearchLocation = 'Hobart, TAS, Australia';
export const sydneySearchLocation = 'Sydney, NSW, Australia';

export const defaultAdults = String(adults);
export const defaultChildren = String(children);
export const defaultInfants = String(infants);
export const defaultSortBy = sortBy;
export const defaultPayWith = PAYMENT_METHODS.CASH;

export const PAY_WITH_INPUT = 'input[name="pay-with-button-group"]';
export const CHECKED_PAY_WITH_INPUT = `${PAY_WITH_INPUT}:checked`;
export const PAY_WITH_LABEL = `${PAY_WITH_INPUT} + label`;

export const USE_CASH = 'CASH';
export const USE_POINTS = 'POINTS';
export const USE_POINTS_PLUS_PAY = 'POINTS PLUS PAY';

export const circleTooltipRegex = /This is a self assigned rating by the service provider or obtained through Expedia\./;
export const starRatingTooltipRegex = /This official star rating is provided by the property, verified by an independent third party\./;
