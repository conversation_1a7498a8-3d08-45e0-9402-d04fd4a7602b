import qs from 'query-string';
import { snakeCaseKeys } from '../helpers';
import { find, flatMap } from 'lodash';
import { getDefaults } from '../../../src/lib/enums/search';
import { addDays, parse, format, subDays } from 'date-fns';
import { HOTELS_PATH, BRAND_CODE } from 'config';

const { VOUCHER_AUTH, VOUCHER_URL, AVA_URL, TEST_PROPERTY_ID } = Cypress.env();

// Set default attributes to the same defaults as in /src/lib/enums/search/DEFAULTS
// eslint-disable-next-line no-unused-vars
const { location, sortBy, ...ATTRIBUTES } = getDefaults();

const MAX_RETRIES = 4;
const incrementDay = (dateString) => format(addDays(parse(dateString, 'yyyy-MM-dd', new Date()), 1), 'yyyy-MM-dd');

// This function will look for the first available offer from a given search, if none is found, it will search on the next day recursively
const requestAvailability = (query, retryCount = 0) => {
  cy.request({ url: `${AVA_URL}/availability`, qs: query }).then(({ body }) => {
    const propertyId = body?.properties?.[0]?.id;
    const offerId = body?.properties?.[0]?.room_types?.[0]?.offers?.[0]?.id;

    if (propertyId && offerId) return { propertyId, offerId, checkIn: query.check_in, checkOut: query.check_out };
    if (retryCount >= MAX_RETRIES) throw new Error(`Could not find an available offer within ${MAX_RETRIES} attempts`);

    requestAvailability({ ...query, check_in: incrementDay(query.check_in), check_out: incrementDay(query.check_out) }, retryCount + 1); // recurse with updated days
  });
};

const requestPropertyAvailability = ({ query, type, payWith }, retryCount = 0) => {
  const stringifiedQuery = qs.stringify(snakeCaseKeys({ ...query, payWith, brand: BRAND_CODE }));
  cy.request(`${AVA_URL}/properties/${TEST_PROPERTY_ID}/offers?${stringifiedQuery}`).then(({ body }) => {
    const offers = flatMap(body.property.room_types, 'offers');
    const offer = find(offers, { type });

    if (offer?.id) return { offer, checkIn: query.checkIn, checkOut: query.checkOut };
    if (retryCount >= MAX_RETRIES) throw new Error(`Could not find an available offer within ${MAX_RETRIES} attempts`);

    requestPropertyAvailability(
      { query: { ...query, checkIn: incrementDay(query.checkIn), checkOut: incrementDay(query.checkOut) }, type, payWith },
      retryCount + 1,
    ); // recurse with updated days
  });
};

Cypress.Commands.add('visitCheckoutWithStayAttributes', ({ type = 'standard', payWith = 'cash' } = {}) => {
  cy.wrap({ query: ATTRIBUTES, type, payWith })
    .then(requestPropertyAvailability)
    .then(({ offer, ...rest }) => {
      const checkoutQuery = qs.stringify({ ...ATTRIBUTES, ...rest });
      cy.visit({
        url: `${HOTELS_PATH}/checkout?propertyId=${TEST_PROPERTY_ID}&offerId=${offer.id}&${checkoutQuery}`,
        retryOnStatusCodeFailure: true,
        retryOnNetworkFailure: true,
      });
    });
});

Cypress.Commands.add('visitCheckoutForClassic', () => {
  const checkIn = format(new Date(), 'yyyy-MM-dd');
  const checkOut = format(addDays(new Date(), 1), 'yyyy-MM-dd');
  const offerQuery = snakeCaseKeys({
    ...ATTRIBUTES,
    checkIn,
    checkOut,
    payWith: 'points',
    brand: BRAND_CODE,
    region_name: location,
    classicOnly: true,
  });

  cy.wrap(offerQuery, { log: false })
    .then(requestAvailability)
    .then(({ propertyId, offerId, checkIn, checkOut }) => {
      cy.visit({
        url: `${HOTELS_PATH}/checkout?propertyId=${propertyId}&offerId=${offerId}`,
        qs: { ...ATTRIBUTES, checkIn, checkOut },
        retryOnStatusCodeFailure: true,
        retryOnNetworkFailure: true,
      });
    });
});

Cypress.Commands.add('visitCheckoutForDepositPay', () => {
  const checkIn = format(addDays(new Date(), 25), 'yyyy-MM-dd');
  const checkOut = format(addDays(new Date(), 26), 'yyyy-MM-dd');
  const offerQuery = snakeCaseKeys({
    ...ATTRIBUTES,
    checkIn,
    checkOut,
    depositOnly: true,
    brand: BRAND_CODE,
    region_name: location,
  });

  cy.wrap(offerQuery, { log: false })
    .then(requestAvailability)
    .then(({ propertyId, offerId, checkIn, checkOut }) => {
      cy.visit({
        url: `${HOTELS_PATH}/checkout?propertyId=${propertyId}&offerId=${offerId}`,
        qs: { ...ATTRIBUTES, checkIn, checkOut },
        retryOnStatusCodeFailure: true,
        retryOnNetworkFailure: true,
      });
    });
});

Cypress.Commands.add('generateVoucher', () => {
  const requestData = {
    method: 'POST',
    url: VOUCHER_URL,
    headers: { Authorization: VOUCHER_AUTH },
    body: {
      voucher: {
        amount: '50.00',
        expiry: '01/01/2050',
        reason_type_code: 'marketing',
        settled_by_code: BRAND_CODE,
        brand_codes: [BRAND_CODE],
      },
    },
  };
  return cy.request(requestData).then((response) => response.body.vouchers[0].code);
});

Cypress.Commands.add('enterIframe', (selector, cb) => {
  cy.frameLoaded(selector);
  cy.enter(selector).then((getBody) => getBody().within(cb));
});

Cypress.Commands.add('setupWaitLoggedIn', () => {
  cy.intercept(`${HOTELS_PATH}/api/ui/member-details/*`).as('member-details');
});

Cypress.Commands.add('waitLoggedIn', () => {
  cy.wait('@member-details');
});

Cypress.Commands.add('setupWaitCheckoutPage', () => {
  cy.intercept('POST', `${HOTELS_PATH}/api/ui/quotes`).as('quotes-results');
});

Cypress.Commands.add('waitCheckoutPage', () => {
  cy.wait('@quotes-results');
});

Cypress.Commands.add('setupWaitDepositPayQuote', () => {
  cy.intercept('POST', `${HOTELS_PATH}/api/ui/quotes`).as('deposit-pay-quote-result');
});

Cypress.Commands.add('waitDepositPayQuote', () => {
  cy.wait('@deposit-pay-quote-result');
});

Cypress.Commands.add('setupStubDepositPayQuote', () => {
  cy.intercept('POST', `${HOTELS_PATH}/api/ui/quotes`, { fixture: 'depositPayQuoteResponse' }).as('deposit-pay-quote-stub');
});

Cypress.Commands.add('waitDepositPayQuote', () => {
  cy.wait('@deposit-pay-quote-stub');
});

Cypress.Commands.add('setupWaitBooking', () => {
  cy.intercept('POST', `${HOTELS_PATH}/api/ui/bookings`).as('booking-request');
});

Cypress.Commands.add('waitBooking', () => {
  cy.wait('@booking-request');
});

Cypress.Commands.add('visitCheckoutWithExcessiveFutureDates', () => {
  const checkIn = format(addDays(new Date(), 2000), 'yyyy-MM-dd');
  const checkOut = format(addDays(new Date(), 2001), 'yyyy-MM-dd');
  const offerQuery = snakeCaseKeys({
    ...ATTRIBUTES,
    checkIn,
    checkOut,
    brand: BRAND_CODE,
    region_name: location,
  });

  cy.wrap({ query: ATTRIBUTES, type: 'standard', payWith: 'cash' })
    .then(requestPropertyAvailability)
    .then(({ offer }) => {
      const checkoutQuery = qs.stringify(offerQuery);
      cy.visit({
        url: `${HOTELS_PATH}/checkout?propertyId=${TEST_PROPERTY_ID}&offerId=${offer.id}&${checkoutQuery}`,
        retryOnStatusCodeFailure: true,
        retryOnNetworkFailure: true,
      });
    });
});

Cypress.Commands.add('visitCheckoutWithOldDates', () => {
  const checkIn = format(subDays(new Date(), 10), 'yyyy-MM-dd');
  const checkOut = format(subDays(new Date(), 9), 'yyyy-MM-dd');
  const offerQuery = snakeCaseKeys({
    ...ATTRIBUTES,
    checkIn,
    checkOut,
    brand: BRAND_CODE,
    region_name: location,
  });

  cy.wrap({ query: ATTRIBUTES, type: 'standard', payWith: 'cash' })
    .then(requestPropertyAvailability)
    .then(({ offer }) => {
      const checkoutQuery = qs.stringify(offerQuery);
      cy.visit({
        url: `${HOTELS_PATH}/checkout?propertyId=${TEST_PROPERTY_ID}&offerId=${offer.id}&${checkoutQuery}`,
        retryOnStatusCodeFailure: true,
        retryOnNetworkFailure: true,
      });
    });
});

Cypress.Commands.add('checkBookingConfirmation', (timeout = 60000) => {
  cy.location('href', { timeout }).should('match', /\/bookings\//);
  cy.findByTestId('confirmation-header').should('exist');
});

Cypress.Commands.add('checkBookingConfirmationPoints', () => {
  cy.findByTestId('points-amount').within(() => {
    cy.findByTestId('currency-text-unhidden')
      .invoke('text')
      .then((text) => expect(text).to.match(/PTS/));
  });
});

Cypress.Commands.add('checkQuoteUnavailable', () => {
  cy.contains(/Sorry, we couldn't make your booking at this time/i).should('be.visible');
  cy.findByText('Change Selection').click();
  cy.location('href', { timeout: 10000 }).should('match', /\/properties\//);
});

Cypress.Commands.add('clickAndWaitDepositPayButton', () => {
  cy.findByTestId('payment-options-form').within(() => {
    cy.findByTestId('deposit-payment-button').click();
    cy.wait('@deposit-pay-quote-result').its('request.body.paidByDeposit').should('eq', 'true');
  });
});

Cypress.Commands.add('validateAdyenDropInStructure', () => {
  cy.get('.adyen-checkout__dropin').within(() => {
    const selectors = [
      '.adyen-checkout__payment-method',
      '.adyen-checkout__payment-method__image__wrapper',
      '.adyen-checkout__card__brands__brand-wrapper',
      '.adyen-checkout-card-input__icon',
      '.adyen-checkout__input',
      '.adyen-checkout__payment-method__name--selected',
      '.adyen-checkout-form-instruction',
      '.adyen-checkout-contextual-text',
      '.adyen-checkout__label__text',
      '.adyen-checkout-contextual-text--error',
    ];

    selectors.forEach((selector) => {
      cy.get(selector).should('exist');
    });
  });
});

Cypress.Commands.add('validateAdyenVaultStructure', () => {
  cy.get('.adyen-checkout__dropin').within(() => {
    const selectors = [
      '.adyen-checkout-payment-methods-container',
      '.adyen-checkout-payment-methods-list-label',
      '.adyen-checkout__payment-methods-list',
      '.adyen-checkout__payment-method',
      '.adyen-checkout__payment-method--credit',
      '.adyen-checkout__payment-method--selected',
      '.adyen-checkout__payment-method__image__wrapper',
      '.adyen-checkout__payment-method__details',
      '.adyen-checkout__payment-method__details__content',
      '.adyen-checkout-form-instruction',
      '.adyen-checkout__input-wrapper',
      '.checkout__input',
    ];

    selectors.forEach((selector) => {
      cy.get(selector).should('exist');
    });
  });
});

Cypress.Commands.add('takeAdyenSnapshot', (snapshotName = 'adyen-default-snapshot', expectedImageCount = 6, failureThreshold = 0.03) => {
  cy.get('[data-testid="adyen-drop-in"]')
    .find('img')
    .should('exist')
    .should('have.length', expectedImageCount)
    .get('[data-testid="adyen-drop-in"]')
    .find('span.adyen-checkout__card__cvc__hint__wrapper')
    .should('exist')
    .then(() => {
      cy.get('.adyen-checkout-form-instruction').invoke('text', "You'll be charged $XXX.00 AUD");
      cy.get('[data-testid="adyen-drop-in"]').matchImageSnapshot(snapshotName, { failureThreshold });
    });
});
