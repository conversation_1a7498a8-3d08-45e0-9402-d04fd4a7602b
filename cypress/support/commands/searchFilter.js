import { HOTELS_PATH } from 'config/env';
import { format, addDays, startOfDay } from 'date-fns';
import { QUERY_DATE_FORMAT, defaultLocation } from '../../support/commonParams';
import { testId } from '../../support/helpers';

const convertResultText = (resultText, resultCountSelector) => {
  let resultCountNumber;

  if (resultCountSelector === 'result-count') {
    const match = resultText.match(/Showing (\d+) available/);
    resultCountNumber = parseInt(match[1]);
  } else {
    resultCountNumber = parseInt(resultText);
  }
  return resultCountNumber;
};

Cypress.Commands.add('visitSearchForPopularFilters', (searchLocation = defaultLocation, searchType = 'list') => {
  const checkInDate = format(startOfDay(addDays(Date.now(), 22)), QUERY_DATE_FORMAT);
  const checkOutDate = format(startOfDay(addDays(Date.now(), 23)), QUERY_DATE_FORMAT);
  const searchMode = searchType === 'list' ? 'list' : 'map';

  cy.visit({
    url: `${HOTELS_PATH}/search/${searchMode}?adults=2&checkIn=${checkInDate}&checkOut=${checkOutDate}&location=${encodeURIComponent(searchLocation)}`,
    retryOnStatusCodeFailure: true,
    retryOnNetworkFailure: true,
  });
});

Cypress.Commands.add('setupSearchList', (searchLocation, popularFilters = false) => {
  cy.setupWaitSearchResults();
  if (popularFilters) {
    cy.visitSearchForPopularFilters(searchLocation, 'list');
  } else {
    cy.visitEmptySearch(searchLocation);
  }
  cy.waitSearchResults();
});

Cypress.Commands.add('setupSearchMap', (buttonText, searchLocation = defaultLocation, popularFilters = false) => {
  cy.setupWaitSearchResults();
  if (popularFilters) {
    cy.visitSearchForPopularFilters(searchLocation, 'map');
  } else {
    cy.visitEmptySearch(searchLocation, 'map');
  }
  cy.get(testId('total-results')).should('be.visible');
  cy.get(testId('filter-button')).contains(buttonText).click();
  cy.waitSearchResults();
});

Cypress.Commands.add('clickAndWaitFilter', (filters, filterType) => {
  cy.setupWaitSearchResults();
  filters.forEach((filter) => {
    cy.get(`${filterType}[name="${filter}"]`).first().click();
  });
  cy.waitSearchResults();
});

Cypress.Commands.add('clickAndWaitRatingFilter', (filterType, ratingValue) => {
  cy.get(filterType).filter(`[value="${ratingValue}"]`).first().parent().click();
});

Cypress.Commands.add('checkSingleFilterAndVerifyCount', (filterName, resultCount) => {
  cy.get(`[data-testid="${filterName}-count"]`)
    .invoke('text')
    .then((resultCountText) => {
      const match = resultCountText.match(/\d+/);
      const expectedResultCount = parseInt(match);
      // Verify the results count matches the filter's count.
      cy.get(`${testId(resultCount)}`)
        .first()
        .should(($element) => {
          const text = $element.text();
          const actualResultCount = convertResultText(text, resultCount);

          expect(actualResultCount).to.equal(expectedResultCount);
        });
    });
});

Cypress.Commands.add('checkCombinedFilterAndVerifyCount', (filterNames, resultCount) => {
  let totalExpectedCount = 0;

  cy.wrap(filterNames).each((filterName) => {
    cy.get(`[data-testid="${filterName}-count"]`)
      .invoke('text')
      .then((countText) => {
        const count = parseInt(countText);
        totalExpectedCount += count;
      });
  });

  cy.get(`${testId(resultCount)}`)
    .first()
    .should(($element) => {
      const text = $element.text();
      const actualResultCount = convertResultText(text, resultCount);

      expect(actualResultCount).to.equal(totalExpectedCount);
    });
});

Cypress.Commands.add('assertFilterChecks', (filterName, filterType, queryKey) => {
  cy.clickAndWaitFilter([filterName], filterType);
  const popularFilterKeys = ['freeCancellation', 'classicRewards', 'depositPay', 'luxOfferOnly'];

  if (popularFilterKeys.includes(queryKey)) {
    cy.assertRoutedToQueryParam(queryKey, 'true');
  } else {
    cy.assertRoutedToQueryParam(queryKey, filterName);
  }
  cy.get(`${filterType}:checked`).should('have.attr', 'name', filterName);
});

Cypress.Commands.add('assertSingleFilter', (filterSelector, filterIndex, filterQueryKey, resultCount) => {
  cy.get(filterSelector)
    .eq(filterIndex)
    .should('have.attr', 'name')
    .then((filterName) => {
      cy.assertFilterChecks(filterName, filterSelector, filterQueryKey);
      cy.checkSingleFilterAndVerifyCount(filterName, resultCount);
    });
});

Cypress.Commands.add('assertCombinedFilters', (filterSelector, filterQueryKey, resultCount) => {
  cy.get(filterSelector).then((allFilters) => {
    const filterNames = Object.values(allFilters)
      .slice(0, 2)
      .map((filter) => filter?.getAttribute('name'));

    cy.clickAndWaitFilter(filterNames, filterSelector);
    cy.get(`${testId(resultCount)}`).should('be.visible');

    cy.assertRoutedToQueryParam(filterQueryKey, filterNames.join());
    filterNames.forEach((name) => {
      cy.get(`${filterSelector}[name="${name}"]:checked`).should('exist');
      // we want to check that each facility filter matches the result count
      // where as all the other filters, we want to check the combined result count
      if (filterQueryKey === 'facilities') {
        cy.checkSingleFilterAndVerifyCount(name, resultCount);
      }
    });

    if (filterQueryKey !== 'facilities') {
      cy.checkCombinedFilterAndVerifyCount(filterNames, resultCount);
    }
  });
});

Cypress.Commands.add('checkPageRatings', (selectedRating, ratingDescriptionTestId, suiteName, resultCountTestId) => {
  cy.get('[data-testid="loading-indicator"]').should('not.exist');

  let ratingElements;
  cy.get('body').then(($body) => {
    const isResultsLoading = $body.find('[data-testid="skeleton-loader"]');

    // we don't want the test to fail if there are no search results
    if (isResultsLoading.length > 0) {
      cy.get('[data-testid="skeleton-loader"]').should('not.be.visible');

      if (suiteName.includes('list')) {
        // If on the search list page, get ratings only from within the search-results container (deals hub doesn't get filtered)
        ratingElements = $body.find(`[data-testid="search-results"] [data-testid="${ratingDescriptionTestId}"]`);
      } else {
        ratingElements = $body.find(`[data-testid="${ratingDescriptionTestId}"]`);
      }

      if (ratingElements.length > 0) {
        cy.wrap(ratingElements).each(($el) => {
          const ratingText = $el.text();
          const match = ratingText.match(/(\d+(\.\d+)?) out of 5/);

          if (match) {
            const resultRating = parseFloat(match[1]);
            const selectedRatingFloat = parseFloat(selectedRating);

            expect(resultRating).to.be.gte(selectedRatingFloat);
          }
        });
      }

      const nextLink = $body.find('[data-testid="next-link"]');
      const pageLinks = $body.find('[data-testid="page-link"]');

      // If the next page link exists, click it and call this command again (we want to check each page of the search results)
      if (nextLink.length > 0) {
        cy.get('[data-testid="next-link"]').click();
        cy.get(`${testId(resultCountTestId)}`).should('be.visible', { timeout: 10000 });
        cy.findAllByTestId(`${ratingDescriptionTestId}`).last().should('be.visible');
        cy.checkPageRatings(selectedRating, ratingDescriptionTestId, suiteName, resultCountTestId);
      }
      // if we're on the map page and we have clicked onto the next page, this closes the filter popup, so we want to reopen it for the next test
      else if (suiteName.includes('map') && pageLinks.length !== 0) {
        const buttonText = suiteName.includes('Hotel ratings') ? 'Hotel Rating' : 'Guest Rating';
        cy.get(testId('filter-button')).contains(buttonText).click();
      }
    }
  });
});
