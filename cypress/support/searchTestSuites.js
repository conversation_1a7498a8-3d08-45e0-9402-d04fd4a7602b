import { testId } from './helpers';
import { hobartSearchLocation, sydneySearchLocation, defaultLocation } from './commonParams';

const LIST_CLEAR_FILTERS = testId('clear-filters-button');
const LIST_RESULT_COUNT = 'result-count';
const MAP_CLEAR_BUTTON = testId('clear-button');
const MAP_RESULT_COUNT = 'total-results';
const POPULAR_FILTER_SELECTOR = `${testId('payment-policy-filter')} input[type="checkbox"]`;
const FACILITY_FILTER_SELECTOR = `${testId('facility-filter')} input[type="checkbox"]`;
const PROPERTY_TYPE_FILTER_SELECTOR = `${testId('property-type-filter')} input[type="checkbox"]`;

const filterTestSuites = [
  {
    suiteName: 'Search list - Facilities filter',
    filterQueryKey: 'facilities',
    filterSelector: FACILITY_FILTER_SELECTOR,
    beforeAction: () => cy.setupSearchList(hobartSearchLocation),
    clearButton: LIST_CLEAR_FILTERS,
    resultCount: LIST_RESULT_COUNT,
  },
  {
    suiteName: 'Search list - Property type filter',
    filterQueryKey: 'propertyTypes',
    filterSelector: PROPERTY_TYPE_FILTER_SELECTOR,
    beforeAction: cy.setupSearchList,
    clearButton: LIST_CLEAR_FILTERS,
    resultCount: LIST_RESULT_COUNT,
  },
  {
    suiteName: 'Search list - Area filter',
    filterQueryKey: 'subRegions',
    filterSelector: `${testId('sub-region-filter')} input[type="checkbox"]`,
    beforeAction: cy.setupSearchList,
    clearButton: LIST_CLEAR_FILTERS,
    resultCount: LIST_RESULT_COUNT,
  },
  {
    suiteName: 'Search map - Facility filter',
    filterQueryKey: 'facilities',
    filterSelector: FACILITY_FILTER_SELECTOR,
    beforeAction: () => cy.setupSearchMap('Facilities'),
    clearButton: MAP_CLEAR_BUTTON,
    resultCount: MAP_RESULT_COUNT,
  },
  {
    suiteName: 'Search map - Property type filter',
    filterQueryKey: 'propertyTypes',
    filterSelector: PROPERTY_TYPE_FILTER_SELECTOR,
    beforeAction: () => cy.setupSearchMap('Property Types'),
    clearButton: MAP_CLEAR_BUTTON,
    resultCount: MAP_RESULT_COUNT,
  },
];

const popularFilterTestSuites = [
  {
    suiteName: 'Search list - Free cancellation filter',
    filterQueryKey: 'freeCancellation',
    filterSelector: POPULAR_FILTER_SELECTOR,
    beforeAction: () => cy.setupSearchList(hobartSearchLocation, true),
    clearButton: LIST_CLEAR_FILTERS,
    resultCount: LIST_RESULT_COUNT,
    filterIndex: 0,
  },
  {
    suiteName: 'Search list - Classic rewards filter',
    filterQueryKey: 'classicRewards',
    filterSelector: POPULAR_FILTER_SELECTOR,
    beforeAction: () => cy.setupSearchList(defaultLocation, true),
    clearButton: LIST_CLEAR_FILTERS,
    resultCount: LIST_RESULT_COUNT,
    filterIndex: 1,
  },
  {
    suiteName: 'Search list - Deposit pay filter',
    filterQueryKey: 'depositPay',
    filterSelector: POPULAR_FILTER_SELECTOR,
    beforeAction: () => cy.setupSearchList(defaultLocation, true),
    clearButton: LIST_CLEAR_FILTERS,
    resultCount: LIST_RESULT_COUNT,
    filterIndex: 2,
  },
  {
    suiteName: 'Search list - Lux offers filter',
    filterQueryKey: 'luxOfferOnly',
    filterSelector: POPULAR_FILTER_SELECTOR,
    beforeAction: () => cy.setupSearchList(defaultLocation, true),
    clearButton: LIST_CLEAR_FILTERS,
    resultCount: LIST_RESULT_COUNT,
    filterIndex: 3,
  },
  {
    suiteName: 'Search map - Free cancellation filter',
    filterQueryKey: 'freeCancellation',
    filterSelector: POPULAR_FILTER_SELECTOR,
    beforeAction: () => cy.setupSearchMap('Popular Filters', defaultLocation, true),
    clearButton: MAP_CLEAR_BUTTON,
    resultCount: MAP_RESULT_COUNT,
    filterIndex: 0,
  },
  {
    suiteName: 'Search map - Classic rewards filter',
    filterQueryKey: 'classicRewards',
    filterSelector: POPULAR_FILTER_SELECTOR,
    beforeAction: () => cy.setupSearchMap('Popular Filters', defaultLocation, true),
    clearButton: MAP_CLEAR_BUTTON,
    resultCount: MAP_RESULT_COUNT,
    filterIndex: 1,
  },
  {
    suiteName: 'Search map - Deposit pay filter',
    filterQueryKey: 'depositPay',
    filterSelector: POPULAR_FILTER_SELECTOR,
    beforeAction: () => cy.setupSearchMap('Popular Filters', defaultLocation, true),
    clearButton: MAP_CLEAR_BUTTON,
    resultCount: MAP_RESULT_COUNT,
    filterIndex: 2,
  },
  {
    suiteName: 'Search map - Lux offers filter',
    filterQueryKey: 'luxOfferOnly',
    filterSelector: POPULAR_FILTER_SELECTOR,
    beforeAction: () => cy.setupSearchMap('Popular Filters', defaultLocation, true),
    clearButton: MAP_CLEAR_BUTTON,
    resultCount: MAP_RESULT_COUNT,
    filterIndex: 3,
  },
];

export const allFilterTestSuites = [...filterTestSuites, ...popularFilterTestSuites];

export const ratingFilterTestSuites = [
  {
    suiteName: 'Search list - Hotel ratings',
    filterQueryName: 'Star',
    filterId: 'hotel-rating-filter',
    beforeAction: () => cy.setupSearchList(sydneySearchLocation),
  },
  {
    suiteName: 'Search list - Tripadvisor ratings',
    filterQueryName: 'Tripadvisor',
    filterId: 'tripadvisor-rating-filter',
    beforeAction: () => cy.setupSearchList(sydneySearchLocation),
  },
  {
    suiteName: 'Search map - Hotel ratings',
    filterQueryName: 'Star',
    filterId: 'hotel-rating-filter',
    beforeAction: () => cy.setupSearchMap('Hotel Rating', sydneySearchLocation),
  },
  {
    suiteName: 'Search map - Tripadvisor ratings',
    filterQueryName: 'Tripadvisor',
    filterId: 'tripadvisor-rating-filter',
    beforeAction: () => cy.setupSearchMap('Guest Rating', sydneySearchLocation),
  },
];

export const visualRatingTestSuites = [
  {
    view: 'list',
    platform: 'Desktop',
    container: '[data-testid="search-results"]',
  },
  {
    view: 'list',
    platform: 'Mobile',
    container: '[data-testid="search-results"]',
    isMobile: true,
  },
  {
    view: 'map',
    platform: 'Desktop',
  },
  {
    view: 'map',
    platform: 'Mobile',
    isMobile: true,
    isMapsView: true,
  },
];
