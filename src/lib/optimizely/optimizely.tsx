import React, { ReactNode } from 'react';
import { createInstance, OptimizelyProvider, ReactSDKClient } from '@optimizely/react-sdk';
import { v4 as uuidv4 } from 'uuid';
import { OPTIMIZELY_DATAFILE } from 'config';
import getIsVipUser from 'lib/getIsVipUser';
import getUserGroup from 'lib/getUserGroup';
import getOptimizelyUserId from 'lib/getOptimizelyUserId';
import { OptimizelyExperimentTracker } from './OptimizelyExperimentTracker';

// Create the Optimizely instance *outside* the component
// so it's only created once per application lifecycle.
// This also avoids recreating it on every render of the wrapper.
let optimizely: ReactSDKClient | null = null;

const getOptimizelyInstance = () => {
  if (!optimizely) {
    optimizely = createInstance({
      eventBatchSize: 100,
      eventFlushInterval: 3000,
      odpOptions: {
        disabled: true,
      },
      datafileOptions: {
        autoUpdate: true,
        updateInterval: 60000,
      },
      sdkKey: OPTIMIZELY_DATAFILE,
      logLevel: 'none',
    });
  }
  return optimizely;
};

// Export for testing purposes
export const resetOptimizelyInstance = () => {
  optimizely = null;
};

export const OptimizelyProviderWrapper = ({ children }: { children: ReactNode }) => {
  const optimizelyUserId = getOptimizelyUserId();
  const ghUserGroup = getUserGroup();
  const isVipUser = getIsVipUser();
  const optimizelyInstance = getOptimizelyInstance();

  return (
    <div>
      <OptimizelyProvider
        optimizely={optimizelyInstance}
        user={{
          id: optimizelyUserId || uuidv4(),
          attributes: {
            user_group: ghUserGroup,
            is_vip: isVipUser,
          },
        }}
      >
        <OptimizelyExperimentTracker optimizelyInstance={optimizelyInstance}>{children}</OptimizelyExperimentTracker>
      </OptimizelyProvider>
    </div>
  );
};

OptimizelyProviderWrapper.displayName = 'OptimizelyProviderWrapper';
