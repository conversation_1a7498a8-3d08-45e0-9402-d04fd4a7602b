import { resetState, hasExperimentFlagsChanged } from './OptimizelyExperimentTracker.helper';

describe('hasExperimentFlagsChanged', () => {
  it('returns true if previous flags are empty', () => {
    expect(hasExperimentFlagsChanged([], ['flag1'])).toBe(true);
  });

  it('returns true if experimentFlags are empty', () => {
    expect(hasExperimentFlagsChanged(['flag1'], [])).toBe(true);
  });

  it('returns false if experimentFlags and prevExperimentFlags are empty', () => {
    expect(hasExperimentFlagsChanged([], [])).toBe(false);
  });

  it('returns false if previous flags are not empty and match the current flags', () => {
    expect(hasExperimentFlagsChanged(['flag1'], ['flag1'])).toBe(false);
  });

  it('returns true if previous flags are not empty and does NOT match the current flags', () => {
    expect(hasExperimentFlagsChanged(['flag1'], ['flag1', 'flag2'])).toBe(true);
  });
});

describe('resetState', () => {
  it('returns an object with empty sets and arrays when called', () => {
    const processedFlagsRef = new Set<string>();
    const concatenatedKeys: string[] = [];
    const enabledValues: string[] = [];

    resetState(processedFlagsRef, concatenatedKeys, enabledValues);

    expect(processedFlagsRef.size).toBe(0);
    expect(concatenatedKeys).toEqual([]);
    expect(enabledValues).toEqual([]);
  });

  it('returns an object with empty sets and arrays when called if has values', () => {
    const processedFlagsRef = new Set<string>(['flag1']);
    const concatenatedKeys: string[] = ['key1'];
    const enabledValues: string[] = ['value1'];

    resetState(processedFlagsRef, concatenatedKeys, enabledValues);

    expect(processedFlagsRef.size).toBe(0);
    expect(concatenatedKeys).toEqual([]);
    expect(enabledValues).toEqual([]);
  });
});
