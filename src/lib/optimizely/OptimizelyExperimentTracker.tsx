import React, { ReactNode, useEffect } from 'react';
import { enums, ReactSDKClient } from '@optimizely/react-sdk';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';
import { includes, isEmpty, kebabCase } from 'lodash';
import getOptimizelyUserId from 'lib/getOptimizelyUserId';
import useOptimizelyFeatureEvent from 'hooks/optimizely/useOptimizelyFeatureEvent';
import { optimizelyFeatureDecision } from 'store/ui/uiActions';
import { OptimizelyFeatureDecision } from 'types/events';
import { resetState, hasExperimentFlagsChanged } from './OptimizelyExperimentTracker.helper';

interface OptimizelyExperimentTrackerProps {
  children: ReactNode;
  optimizelyInstance: ReactSDKClient;
}

interface OptimizelyDecisionNotificationPayload {
  type: string;
  userId: string;
  attributes: Record<string, string>;
  decisionInfo: {
    flagKey: string;
    ruleKey: string;
    variationKey: string;
    enabled: boolean;
  };
}

export const OptimizelyExperimentTracker = ({ children, optimizelyInstance }: OptimizelyExperimentTrackerProps) => {
  const dispatch = useDispatch();
  const { isReady } = useRouter();

  const { isReady: flagsReady, experimentFlags } = useOptimizelyFeatureEvent();

  const optimizelyUserId = getOptimizelyUserId();

  useEffect(() => {
    const processedFlags = new Set<string>();
    const concatenatedKeys: string[] = [];
    const enabledValues: string[] = [];
    let prevExperimentFlags: string[] = [];

    if (!isReady || !flagsReady || !optimizelyUserId || isEmpty(experimentFlags)) return;

    const listenerId = optimizelyInstance.notificationCenter.addNotificationListener(
      enums.NOTIFICATION_TYPES.DECISION,
      (payload: OptimizelyDecisionNotificationPayload) => {
        if (!payload) return;

        const { decisionInfo } = payload;
        const { flagKey, ruleKey, variationKey, enabled } = decisionInfo;

        if (includes(experimentFlags, flagKey) && !processedFlags.has(flagKey)) {
          const concatenatedKey = [flagKey, variationKey, ruleKey].map(kebabCase).join('_');
          concatenatedKeys.push(concatenatedKey);
          enabledValues.push(enabled ? 'true' : 'false');
          processedFlags.add(flagKey);

          const allFlagsProcessed = processedFlags.size === experimentFlags.length;
          if (allFlagsProcessed) {
            const fxp_variant_string = concatenatedKeys.join(',');
            const enabled_string = enabledValues.join(',');

            const analyticsPayload: OptimizelyFeatureDecision = {
              fxp_variant_string,
              enabled_string,
            };

            dispatch(optimizelyFeatureDecision(analyticsPayload));
          }
        }
      },
    );

    const flagsChanged = hasExperimentFlagsChanged(prevExperimentFlags, experimentFlags);
    if (flagsChanged || !prevExperimentFlags) {
      resetState(processedFlags, concatenatedKeys, enabledValues);
    }

    prevExperimentFlags = experimentFlags;

    return () => {
      optimizelyInstance.notificationCenter.removeNotificationListener(listenerId);
    };
  }, [dispatch, isReady, flagsReady, optimizelyUserId, optimizelyInstance, experimentFlags]);

  return <>{children}</>;
};
