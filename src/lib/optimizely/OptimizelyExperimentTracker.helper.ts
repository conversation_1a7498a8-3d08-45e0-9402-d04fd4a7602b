import { includes } from 'lodash';

export const resetState = (processedFlags: Set<string>, concatenatedKeys: string[], enabledValues: string[]) => {
  processedFlags.clear();
  concatenatedKeys.length = 0;
  enabledValues.length = 0;
};

export const hasExperimentFlagsChanged = (prevExperimentFlags: string[], experimentFlags: string[]): boolean => {
  if (!prevExperimentFlags) {
    return experimentFlags.length > 0;
  }

  return prevExperimentFlags.length !== experimentFlags.length || !prevExperimentFlags.every((flag) => includes(experimentFlags, flag));
};
