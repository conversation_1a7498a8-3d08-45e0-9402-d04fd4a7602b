import React from 'react';
import { render, screen } from '@testing-library/react';
import { createInstance, OptimizelyProvider } from '@optimizely/react-sdk';
import { v4 as uuidv4 } from 'uuid';
import { OptimizelyProviderWrapper, resetOptimizelyInstance } from './optimizely';
import getIsVipUser from 'lib/getIsVipUser';
import getUserGroup from 'lib/getUserGroup';
import getOptimizelyUserId from 'lib/getOptimizelyUserId';

jest.mock('uuid');

jest.mock('@optimizely/react-sdk', () => ({
  createInstance: jest.fn(() => ({
    notificationCenter: {
      addNotificationListener: jest.fn(),
      removeNotificationListener: jest.fn(),
    },
  })),
  OptimizelyProvider: jest.fn(({ children }) => <div data-testid="mock-optimizely-provider">{children}</div>),
}));

jest.mock('./OptimizelyExperimentTracker', () => ({
  OptimizelyExperimentTracker: jest.fn(({ children }) => <div data-testid="mock-experiment-tracker">{children}</div>),
}));

jest.mock('config', () => ({
  OPTIMIZELY_DATAFILE: 'mock-sdk-key',
}));

jest.mock('lib/getIsVipUser', () => jest.fn());
jest.mock('lib/getUserGroup', () => jest.fn());
jest.mock('lib/getOptimizelyUserId', () => jest.fn());
const mockOptimizelyProvider = OptimizelyProvider as jest.Mock;

const renderWithChildren = (children: React.ReactNode) => {
  return render(<OptimizelyProviderWrapper>{children}</OptimizelyProviderWrapper>);
};

describe('OptimizelyProviderWrapper', () => {
  const mockCreateInstance = createInstance as jest.Mock;
  const mockGetIsVipUser = getIsVipUser as jest.Mock;
  const mockGetUserGroup = getUserGroup as jest.Mock;
  const mockGetOptimizelyUserId = getOptimizelyUserId as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();

    (uuidv4 as jest.Mock).mockReturnValue('random-id');
    resetOptimizelyInstance();
    mockCreateInstance.mockReturnValue({
      notificationCenter: {
        addNotificationListener: jest.fn(),
        removeNotificationListener: jest.fn(),
      },
    });
    mockGetOptimizelyUserId.mockReturnValue('test-user-id');
    mockGetUserGroup.mockReturnValue('test-user-group');
    mockGetIsVipUser.mockReturnValue(false);
  });

  it('renders children inside OptimizelyProvider', () => {
    renderWithChildren(<span>Test Child</span>);

    expect(screen.getByTestId('mock-optimizely-provider')).toBeInTheDocument();
    expect(screen.getByTestId('mock-experiment-tracker')).toBeInTheDocument();
    expect(screen.getByText('Test Child')).toBeInTheDocument();
  });

  it('calls createInstance only once per lifecycle', () => {
    renderWithChildren(<span>First</span>);
    renderWithChildren(<span>Second</span>);

    expect(mockCreateInstance).toHaveBeenCalledTimes(1);
  });

  it('passes correct user attributes when all values are present', () => {
    mockGetOptimizelyUserId.mockReturnValue('user-123');
    mockGetUserGroup.mockReturnValue('gold');
    mockGetIsVipUser.mockReturnValue(true);

    renderWithChildren(<span>Child</span>);

    expect(mockOptimizelyProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        user: {
          id: 'user-123',
          attributes: {
            user_group: 'gold',
            is_vip: true,
          },
        },
      }),
      {},
    );
  });

  it('uses uuid fallback for user id if optimizelyUserId is undefined', () => {
    mockGetOptimizelyUserId.mockReturnValue(undefined);

    renderWithChildren(<span>Child</span>);

    expect(mockOptimizelyProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        user: expect.objectContaining({ id: 'random-id' }),
      }),
      {},
    );
  });

  it('creates Optimizely instance with correct configuration', () => {
    renderWithChildren(<span>Child</span>);

    expect(mockCreateInstance).toHaveBeenCalledWith({
      eventBatchSize: 100,
      eventFlushInterval: 3000,
      odpOptions: {
        disabled: true,
      },
      datafileOptions: {
        autoUpdate: true,
        updateInterval: 60000,
      },
      sdkKey: 'mock-sdk-key',
      logLevel: 'none',
    });
  });

  it('creates a new Optimizely instance after reset', () => {
    renderWithChildren(<span>Initial Render</span>);
    expect(mockCreateInstance).toHaveBeenCalledTimes(1);

    resetOptimizelyInstance();

    renderWithChildren(<span>After Reset</span>);
    expect(mockCreateInstance).toHaveBeenCalledTimes(2);
  });
});
