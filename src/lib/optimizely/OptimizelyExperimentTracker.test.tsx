import React from 'react';
import { render, screen } from '@testing-library/react';
import { useDispatch } from 'react-redux';
import { enums, useDecision, ReactSDKClient } from '@optimizely/react-sdk';
import { useRouter } from 'next/router';
import { OptimizelyExperimentTracker } from './OptimizelyExperimentTracker';
import getOptimizelyUserId from 'lib/getOptimizelyUserId';
import { resetState, hasExperimentFlagsChanged } from './OptimizelyExperimentTracker.helper';

jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
}));

jest.mock('@optimizely/react-sdk', () => ({
  useDecision: jest.fn(),
  enums: {
    NOTIFICATION_TYPES: {
      DECISION: 'DECISION',
    },
  },
}));

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('lib/getOptimizelyUserId', () => jest.fn());
jest.mock('./OptimizelyExperimentTracker.helper', () => ({
  resetState: jest.fn(),
  hasExperimentFlagsChanged: jest.fn(),
}));

const decisionInfo = (key) => ({
  userId: 'test-user-id',
  decisionInfo: {
    flagKey: `flag${key}`,
    ruleKey: `rule${key}`,
    variationKey: `variation${key}`,
    enabled: true,
  },
});

const mockAddListener = jest.fn();
const mockRemoveListener = jest.fn();

const mockOptimizelyInstance = {
  notificationCenter: {
    addNotificationListener: mockAddListener,
    removeNotificationListener: mockRemoveListener,
  },
} as unknown as ReactSDKClient;

const renderOptimizelyExperimentTracker = () => {
  return render(
    <OptimizelyExperimentTracker optimizelyInstance={mockOptimizelyInstance}>
      <div>Child</div>
    </OptimizelyExperimentTracker>,
  );
};

describe('OptimizelyExperimentTracker', () => {
  const mockDispatch = jest.fn();

  const mockUseDecision = useDecision as jest.Mock;
  const mockUseRouter = useRouter as jest.Mock;
  const mockResetState = resetState as jest.Mock;
  const mockHasExperimentFlagsChanged = hasExperimentFlagsChanged as jest.Mock;
  const mockGetOptimizelyUserId = getOptimizelyUserId as jest.Mock;

  beforeEach(() => {
    jest.resetAllMocks();

    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
    mockUseRouter.mockReturnValue({ isReady: true });
    mockGetOptimizelyUserId.mockReturnValue('test-user-id');
    mockResetState.mockReturnValue({});
    mockHasExperimentFlagsChanged.mockReturnValue(false);

    mockUseDecision.mockReturnValue([
      {
        variables: {
          experiment_flags: {
            experimentFlags: ['flag1'],
          },
        },
      },
      true,
    ]);
    mockAddListener.mockImplementation((type, callback) => {
      if (type === enums.NOTIFICATION_TYPES.DECISION) {
        callback(decisionInfo(1));
      }
      return 'listener-id';
    });
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('renders the children elements passed to the tracker', () => {
    render(
      <OptimizelyExperimentTracker optimizelyInstance={mockOptimizelyInstance}>
        <div>Test Child</div>
      </OptimizelyExperimentTracker>,
    );
    expect(screen.getByText('Test Child')).toBeInTheDocument();
  });

  it('registers a notification listener when the component is ready', () => {
    renderOptimizelyExperimentTracker();

    expect(mockAddListener).toHaveBeenCalledWith(enums.NOTIFICATION_TYPES.DECISION, expect.any(Function));
  });

  it('removes the notification listener when the component unmounts', () => {
    const { unmount } = renderOptimizelyExperimentTracker();

    unmount();

    expect(mockRemoveListener).toHaveBeenCalledWith('listener-id');
  });

  describe('when experimentFlags contains one or more keys', () => {
    describe('and the decisionInfo matches a flag in experimentFlags', () => {
      it('dispatches the correct fxp_variant_string when a matching decision is received', () => {
        renderOptimizelyExperimentTracker();

        // mockAddListener.mock.calls[0][1] accesses the second argument (callback function)
        // passed to the first call of mockAddListener. This allows us to manually invoke
        // the callback to simulate the decision notification.
        mockAddListener.mock.calls[0][1](decisionInfo(1));

        expect(mockDispatch).toHaveBeenCalledWith(
          expect.objectContaining({
            payload: expect.objectContaining({
              fxp_variant_string: 'flag-1_variation-1_rule-1',
            }),
          }),
        );
      });

      it('dispatches the correct enabled_string value for a matching decision', () => {
        renderOptimizelyExperimentTracker();

        expect(mockDispatch).toHaveBeenCalledWith(
          expect.objectContaining({
            payload: expect.objectContaining({
              enabled_string: 'true',
            }),
          }),
        );
      });

      describe('when there are multiple flags to be processed', () => {
        beforeEach(() => {
          mockUseDecision.mockReturnValue([
            {
              variables: {
                experiment_flags: {
                  experimentFlags: ['flag1', 'flag2'],
                },
              },
            },
            true,
          ]);
        });

        it('does not dispatch until all experimentFlags have been processed', () => {
          renderOptimizelyExperimentTracker();

          mockAddListener.mock.calls[0][1](decisionInfo(1));
          expect(mockDispatch).not.toHaveBeenCalled();

          mockAddListener.mock.calls[0][1](decisionInfo(2));
          expect(mockDispatch).toHaveBeenCalled();
        });

        it('dispatches when all experimentFlags are processed, regardless of the order', () => {
          renderOptimizelyExperimentTracker();

          mockAddListener.mock.calls[0][1](decisionInfo(2));
          mockAddListener.mock.calls[0][1](decisionInfo(1));

          expect(mockDispatch).toHaveBeenCalled();
        });
      });
    });

    describe('and the decisionInfo does not match any flag in experimentFlags', () => {
      it('does not dispatch a concatenated fxp_variant_string for non-matching decisions', () => {
        renderOptimizelyExperimentTracker();

        mockAddListener.mock.calls[0][1](decisionInfo(1));
        mockAddListener.mock.calls[0][1](decisionInfo(2));

        expect(mockDispatch).not.toHaveBeenCalledWith(
          expect.objectContaining({
            payload: expect.objectContaining({
              fxp_variant_string: 'flag-1_variation-1_rule-1,flag-2_variation-2_rule-2',
            }),
          }),
        );
      });

      it('does not dispatch an enabled_string for non-matching decisions', () => {
        renderOptimizelyExperimentTracker();

        mockAddListener.mock.calls[0][1](decisionInfo(1));
        mockAddListener.mock.calls[0][1](decisionInfo(2));

        expect(mockDispatch).not.toHaveBeenCalledWith(
          expect.objectContaining({
            payload: expect.objectContaining({
              enabled_string: 'true,true',
            }),
          }),
        );
      });
    });
  });

  it('dispatches again if experimentFlags changes between renders', () => {
    renderOptimizelyExperimentTracker();

    mockAddListener.mock.calls[0][1](decisionInfo(1));

    expect(mockDispatch).toHaveBeenCalledTimes(1);

    renderOptimizelyExperimentTracker();

    mockUseDecision.mockReturnValueOnce([
      {
        variables: {
          experiment_flags: {
            experimentFlags: ['flag1', 'flag2'],
          },
        },
      },
      true,
    ]);

    mockAddListener.mock.calls[0][1](decisionInfo(1));
    mockAddListener.mock.calls[0][1](decisionInfo(2));

    expect(mockDispatch).toHaveBeenCalledTimes(2);
  });

  it('does not dispatch multiple times for duplicate flag decisions', () => {
    renderOptimizelyExperimentTracker();

    mockAddListener.mock.calls[0][1](decisionInfo(1));
    mockAddListener.mock.calls[0][1](decisionInfo(2));
    mockAddListener.mock.calls[0][1](decisionInfo(1));

    expect(mockDispatch).toHaveBeenCalledTimes(1);
    expect(mockDispatch).not.toHaveBeenCalledWith(
      expect.objectContaining({
        payload: expect.objectContaining({
          enabled_string: 'true',
          fxp_variant_string: 'flag-1_variation-1_rule-1',
        }),
        type: 'ui/OPTIMIZELY_FEATURE_DECISION',
        0: expect.any(Number),
      }),
    );
  });
});
