import type { BlockContent, SanityImage, ValueProposition } from '@qga/sanity-components/dist/types';

export interface Destination {
  name: string;
  slug: string;
  regionId: string;
  // FIXME: fix type in @qga/sanity-components
  image: Omit<SanityImage, 'asset'> & {
    asset: import('@sanity/image-url/lib/types/types').SanityImageSource;
  };
}

export interface Region {
  name: string;
  slug?: string;
  fullName: string;
  id: string;
  url?: string;
  image?: {
    name: string;
    asset: SanityImage;
  };
}

export interface Offer {
  name: string;
  description?: BlockContent;
  promotionalSash?: string;
  callToAction?: Link;
  image?: {
    name: string;
    asset: SanityImage;
  };
  width: string;
  height: string;
}

export interface Link {
  text: string;
  url: string;
  external: boolean;
}

export interface Category {
  category: string;
  destinations: Destination[];
}

export interface DetailedBanner {
  content: string;
  cta?: {
    href: string;
    iconName?: string;
    label: string;
    type: 'primary' | 'outline' | 'text';
  };
  heading: string;
  icon: string;
}

export interface ExclusiveOfferInclusion {
  iconName: string;
  title: string;
}

export interface ExclusiveOfferRoom {
  id: string;
  title?: string;
  description?: string;
  name?: string;
  adults: number;
  children?: number;
  infants?: number;
  hasValuedAtPrice?: boolean;
  inclusions: Array<ExclusiveOfferInclusion>;
  minNumberOfNights: number;
  offerTotal: {
    amount: string;
    currency: string;
    termsConditionsSymbol?: string;
  };

  valuedAtTotal?: {
    amount: string;
    currency: string;
    termsConditionsSymbol?: string;
  };
}

export interface ExclusiveOffer {
  property: {
    id: string;
    address: ExtranetAddress;
    images: Array<ExtranetImage>;
    name: string;
  };

  rooms?: Array<{ offers: ExclusiveOfferRoom[]; extranet: { name: string; id: string } }>;
  extended?: boolean;
  id?: string;
  saleDates?: DateRange;
  terms?: string;
  travelDates?: DateRange;
  valuePropositions?: Array<ValueProposition>;
  inclusionSets?: { _key?: string }[];
  highlights?: { description: string; inclusions: Array<{ code: string; name: string; subTitle: string }> };
}

export interface PropertyExclusiveOffersPage {
  valuePropositions: Array<ValueProposition>;
}

export interface RichDate {
  local: string;
}

export interface DateRange {
  start?: string | RichDate;
  end?: string | RichDate;
}

export interface ExtranetAddress {
  streetAddress: string[];
  suburb?: string;
  state?: string;
  country: string;
  countryCode: string;
}

export interface ExtranetImage {
  caption: string;
  urlLarge: string;
  urlMedium: string;
  urlSmall: string;
}

export enum ButtonType {
  Primary = 'primary',
  Secondary = 'secondary',
  Link = 'link',
}

export enum Alignment {
  Left = 'flex-start',
  Center = 'center',
  Right = 'flex-end',
}

export enum LinkType {
  Anchor = 'anchor',
  External = 'external',
}
export interface CallToAction {
  _key: string;
  text: string;
  buttonType: ButtonType;
  linkType: LinkType;
  isNewTab: boolean;
  anchorLink?: string;
  url?: string;
  alignment?: Alignment;
  onClick?: () => void;
  trackingEvent: {
    action: string;
    category: string;
    label?: string;
  };
}
export interface AirbnbTab {
  isLandingPage: boolean;
}
export interface CampaignBanner {
  heading?: {
    name: string;
    slug: string;
  };
  heroImage?: Omit<SanityImage, 'asset'> & {
    asset: import('@sanity/image-url/lib/types/types').SanityImageSource;
  };
  description?: BlockContent[];
  endDate?: string;
  onCallToActionClick?: () => void;
  promotionalSash?: string;
  callToAction?: CallToAction[];
}

export interface FeaturedOffers {
  featuredOffers: Array<FeaturedOffer>;
}

export interface ActivitiesStaticImage {
  ctaText: string | null;
  description: string | null;
  image: object | null;
  pointsPerDollar: number | null;
  title: string | null;
}

export interface FeaturedOffer {
  backgroundImage?: {
    name: string;
    asset: SanityImage;
  };
  callToAction?: object;
  name?: string;
  offerDescription?: Array<object>;
  promotionalSash?: string;
  saleDateRange?: object;
  saleDates?: object;
  terms?: string;
  termsShort?: string;
  travelDateRange?: object;
  travelDates?: object;
}
export interface FeaturedOfferBanner {
  heading?: string;
  description?: BlockContent[];
  promotionalSash?: string;
  callToAction?: {
    text?: string;
    type?: ButtonType;
    url?: string;
    to?: string;
    target?: string;
    onClick?: () => void;
  };
}

export interface ExclusiveOfferResultsType {
  crossSellBanner: DetailedBanner;
  offers: Array<ExclusiveOffer>;
}
