import { AvaCurrency, AvaPointsEarn } from 'types/ava';

export type RatingType = 'AAA' | 'SELF_RATED' | null | undefined;
export interface CustomerRating {
  averageRating: number;
  ratingImageUrl: string;
  reviewCount: number;
  source: string;
}
export interface PropertyCardProps {
  country: string;
  checkIn: Date;
  checkOut: Date;
  customerRating: CustomerRating;
  id: string;
  featuredOfferId?: string;
  recommended?: boolean;
  imageAltTag: string;
  imageSrc: string;
  imageSrcSet?: string;
  inline?: boolean;
  isNonRefundable?: boolean;
  offerId: string;
  offerType: string | null;
  onClick?: () => void;
  pointsEarned: AvaPointsEarn;
  promotionName?: string;
  propertyName: string;
  rating: number;
  ratingType: RatingType;
  showCampaignMessage?: boolean;
  showRatingTooltip?: boolean;
  total: AvaCurrency;
  totalCash?: AvaCurrency;
  totalBeforeDiscount: AvaCurrency;
  totalDiscount: AvaCurrency;
  to?: string;
  isPromoCard?: boolean;
  showRedSash?: boolean;
}

export interface Property {
  id: string;
  name: string;
  description: string;
  category: string;
  address: { streetAddress: string; suburb: string; state: string; postcode: string; country: string; countryCode: string };
  images: Array<{ caption: string; urlSmall: string; urlOriginal: string; urlMedium: string; urlLarge: string }>;
  featuredOfferId?: string;
  mainImage: { caption: string; urlSmall: string; urlOriginal: string; urlMedium: string; urlLarge: string };
  customerRatings: CustomerRating[];
  roomType: {
    name: string;
    id: string;
    maxOccupantCount: number;
    offer: Array<Offer>;
    images: Array<{ caption: string; urlSmall: string; urlOriginal: string; urlMedium: string; urlLarge: string }>;
    featuredOfferId?: string;
    mainImage: { caption: string; urlSmall: string; urlOriginal: string; urlMedium: string; urlLarge: string };
    RoomTypeFacilities: object;
  };
  roomTypes: Array<RoomType>;
  ratingType: RatingType;
  rating: number;
  promotionSashes: string[];
}

export interface RoomType {
  name: string;
  id: string;
  maxOccupantCount: number;
  offer: Array<Offer>;
  images: Array<{ caption: string; urlSmall: string; urlOriginal: string; urlMedium: string; urlLarge: string }>;
  featuredOfferId?: string;
  mainImage: { caption: string; urlSmall: string; urlOriginal: string; urlMedium: string; urlLarge: string };
  RoomTypeFacilities: object;
}
export interface Inclusion {
  description?: string;
  name?: string;
  code?: string;
  subtitle?: string;
  icon?: string;
}
export interface Offer {
  allocationsAvailable?: number;
  name: string;
  type: string;
  id: string;
  description?: string;
  inclusions?: Inclusion[];
  luxOffer?: boolean;
  cancellationPolicy: { isNonrefundable: boolean; description: string; cancellationWindows: CancellationWindow[] };
  charges: Charges;
  pointsEarned: {
    maxQffEarnPpd: number;
    maxQbrEarnPpd: number;
    qbrPoints: { total: number };
    qffPoints: { qffPointsClub: number; total: number; bonus: number; base: number };
    promotionMultiplier: number;
    propertyPpd: number;
  };
  promotion: {
    description?: string;
    name?: string;
    promotionCode?: string;
    priority?: number;
    qffPointsMultiplier?: number;
    campaignCode?: string;
  } | null;
  depositPay: { depositPayable: boolean };
  valueAdds: string[];
  pointsTierInstanceId: string;
}

export interface Charges {
  total: { amount: string; currency: string };
  totalBeforeDiscount: { amount: string; currency: string };
  totalDiscount: { amount: number; currency: string };
  totalCash: { amount: string; currency: string };
  payableAtBooking: { total: { amount: string; currency: string } };
  payableAtProperty: { total: { amount: string; currency: string } };
  strikethrough?: {
    price: { currency: string | null; amount: string | null };
    discount: { currency: string | null; amount: string | null };
    percentage: string | null;
  };
}

export interface OfferAvailability {
  description: string;
  cancellationPolicy: CancellationPolicy;
  name: string;
  charges: Charges;
  memberDealAvailable: boolean;
  depositPay: DepositPay;
  type: string;
  inclusions: Inclusion[];
  otaRatePlanCode: string;
  bedGroups: BedGroup[];
  pointsTierInstanceId: string;
  id: string;
  pointsEarned: PointsEarned;
  promotion: Promotion;
  valueAdds: string[];
  payableAtProperty: { total: { amount: string; currency: string } };
  total?: string;
  totalBeforeDiscount?: string;
  totalCash: string;
}
export interface CancellationPolicy {
  description: string;
  cancellationWindows: CancellationWindow[];
  isNonrefundable: boolean;
}
export interface CancellationWindow {
  startTime: string;
  percentage: string;
  endTime: string;
  currency: string;
  formattedBeforeDate: string;
}
export interface CurrencyValue {
  amount: number | string;
  currency: string;
}

export interface PayableAtProperty {
  breakdown: { description: string; charge: string };
  total: CurrencyValue;
}

export interface PayableAtBooking {
  total: CurrencyValue;
}

export interface DepositPay {
  depositPayable: boolean;
}

export interface BedGroup {
  description: string;
  bedTypeId: string;
  bedTypes: BedType[];
}

export interface BedType {
  type: string;
  size: string;
  quantity: number;
}

export interface PointsEarned {
  maxQffEarnPpd: number;
  qbrPoints: QbrPoints;
  maxQbrEarnPpd: number;
  qffPoints: QffPoints;
  promotionMultiplier: number;
  propertyPpd: number;
}

export interface QbrPoints {
  total: number;
}

export interface QffPoints {
  qffPointsClub: number;
  total: number;
  bonus: number;
  base: number;
}

export interface Promotion {
  description: string;
  name: string;
  promotionCode: string;
  priority: number;
  campaignCode: string;
}

export interface TripAdvisorRatings {
  rating: { averageRating: number | null; reviewCount: number };
  small: boolean;
  displayReviews: boolean;
  underlined: string | boolean;
  reviewCount: number;
}

export interface Ga4Offer {
  charges: {
    total: {
      amount: string;
      currency: string;
    };
  };
}

export interface Ga4RoomType {
  name: string;
}

export interface Ga4Property {
  id: string;
  name: string;
  category: string;
  checkIn: string;
  checkOut: string;
}

export interface emitPageResults {
  results: {
    offer: Ga4Offer;
    roomType: Ga4RoomType;
    property: Ga4Property;
  }[];
  regionName: string;
}
