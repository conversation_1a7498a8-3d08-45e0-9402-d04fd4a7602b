import { useDecision } from '@optimizely/react-sdk';

type ReturnVariables = {
  tooltip: string;
};

interface Result {
  isReady: boolean;
  isPointsRedemptionStrikethrough: boolean;
  tooltipMessage: string;
}

const FEATURE_FLAG_NAME = 'qantas-hotels-points-redemption-strikethrough';

const usePointsRedemptionStrikethrough = (): Result => {
  const [decision, isReady] = useDecision(FEATURE_FLAG_NAME, {
    autoUpdate: true,
  });

  const variables = decision.variables as ReturnVariables;

  return {
    isReady,
    isPointsRedemptionStrikethrough: decision.enabled,
    tooltipMessage: variables.tooltip,
  };
};

export default usePointsRedemptionStrikethrough;
