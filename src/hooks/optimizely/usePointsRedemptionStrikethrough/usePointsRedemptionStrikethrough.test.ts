import { renderHook } from '@testing-library/react-hooks';
import { useDecision } from '@optimizely/react-sdk';
import usePointsRedemptionStrikethrough from './usePointsRedemptionStrikethrough';

jest.mock('@optimizely/react-sdk', () => ({
  useDecision: jest.fn(),
}));

describe('usePointsRedemptionStrikethrough', () => {
  beforeEach(() => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: false, variables: { tooltip: '' } }, false]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('returns default state when optimizely is not ready', () => {
    const { result } = renderHook(() => usePointsRedemptionStrikethrough());

    expect(result.current).toEqual({
      isReady: false,
      isPointsRedemptionStrikethrough: false,
      tooltipMessage: '',
    });
  });

  it('returns usePointsRedemptionStrikethrough as true when when feature flag is on', () => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: true, variables: { tooltip: '' } }, true]);

    const { result } = renderHook(() => usePointsRedemptionStrikethrough());

    expect(result.current.isPointsRedemptionStrikethrough).toBeTruthy();
  });

  it('returns usePointsRedemptionStrikethrough as false when feature flag is off', () => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: false, variables: { tooltip: '' } }, true]);

    const { result } = renderHook(() => usePointsRedemptionStrikethrough());

    expect(result.current.isPointsRedemptionStrikethrough).toBeFalsy();
  });
});
