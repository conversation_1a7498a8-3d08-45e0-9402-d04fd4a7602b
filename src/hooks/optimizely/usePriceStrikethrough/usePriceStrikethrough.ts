import { useDecision } from '@optimizely/react-sdk';

interface Result {
  isReady: boolean;
  showFullTraffic: boolean;
  showVariationB: boolean;
}

const FEATURE_FLAG_NAME = 'qantas-hotels-price-strikethrough';

const usePriceStrikethrough = (): Result => {
  const [decision, isReady] = useDecision(FEATURE_FLAG_NAME, {
    autoUpdate: true,
  });

  return {
    isReady,
    showFullTraffic: decision.enabled && decision.variationKey === 'strikethrough_-_show',
    showVariationB: decision.enabled && decision.variationKey === 'strikethrough_variation_b_-_show_strikethrough',
  };
};

export default usePriceStrikethrough;
