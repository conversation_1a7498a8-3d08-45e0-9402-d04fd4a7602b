import { renderHook } from '@testing-library/react-hooks';
import { useDecision } from '@optimizely/react-sdk';
import usePriceStrikethrough from './usePriceStrikethrough';

jest.mock('@optimizely/react-sdk', () => ({
  useDecision: jest.fn(),
}));

describe('usePriceStrikethrough', () => {
  beforeEach(() => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: false }, false]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('returns default state when optimizely is not ready', () => {
    const { result } = renderHook(() => usePriceStrikethrough());

    expect(result.current).toEqual({
      isReady: false,
      showFullTraffic: false,
      showVariationB: false,
    });
  });

  it('returns showFullTraffic as true when feature flag is on and variation is strikethrough_-_show', () => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: true, variationKey: 'strikethrough_-_show' }, true]);

    const { result } = renderHook(() => usePriceStrikethrough());

    expect(result.current.showFullTraffic).toBe(true);
  });

  it('returns showFullTraffic as false when feature flag is off', () => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: false }, true]);

    const { result } = renderHook(() => usePriceStrikethrough());

    expect(result.current.showFullTraffic).toBe(false);
  });

  it('returns showVariationB as true when feature flag is on and variation is strikethrough_variation_b_-_show_strikethrough', () => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: true, variationKey: 'strikethrough_variation_b_-_show_strikethrough' }, true]);

    const { result } = renderHook(() => usePriceStrikethrough());

    expect(result.current.showVariationB).toBe(true);
  });

  it('returns showVariationB as false when feature flag is off', () => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: false }, true]);

    const { result } = renderHook(() => usePriceStrikethrough());

    expect(result.current.showVariationB).toBe(false);
  });
});
