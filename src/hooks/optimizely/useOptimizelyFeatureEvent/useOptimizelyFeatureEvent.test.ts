import { renderHook } from '@testing-library/react-hooks';
import { useDecision } from '@optimizely/react-sdk';
import useOptimizelyFeatureEvent from './useOptimizelyFeatureEvent';

jest.mock('@optimizely/react-sdk', () => ({
  useDecision: jest.fn(),
}));

describe('useOptimizelyFeatureEvent', () => {
  beforeEach(() => {
    (useDecision as jest.Mock).mockReturnValue([
      {
        enabled: false,
        variables: {
          experiment_flags: {
            experimentFlags: [],
          },
        },
      },
      false,
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('returns default state when optimizely is not ready', () => {
    const { result } = renderHook(() => useOptimizelyFeatureEvent());

    expect(result.current).toEqual({
      isReady: false,
      isOptimizelyFeatureEvent: false,
      experimentFlags: [],
    });
  });

  it('returns useOptimizelyFeatureEvent as true when when feature flag is on', () => {
    (useDecision as jest.Mock).mockReturnValue([
      {
        enabled: true,
        variables: {
          experiment_flags: {
            experimentFlags: [],
          },
        },
      },
      true,
    ]);

    const { result } = renderHook(() => useOptimizelyFeatureEvent());

    expect(result.current.isOptimizelyFeatureEvent).toBeTruthy();
  });

  it('returns useOptimizelyFeatureEvent as false when feature flag is off', () => {
    (useDecision as jest.Mock).mockReturnValue([
      {
        enabled: false,
        variables: {
          experiment_flags: {
            experimentFlags: [],
          },
        },
      },
      true,
    ]);

    const { result } = renderHook(() => useOptimizelyFeatureEvent());

    expect(result.current.isOptimizelyFeatureEvent).toBeFalsy();
  });

  it('returns useOptimizelyFeatureEvent as false when variables are not present', () => {
    (useDecision as jest.Mock).mockReturnValue([
      {
        variables: null,
      },
    ]);

    const { result } = renderHook(() => useOptimizelyFeatureEvent());

    expect(result.current.isOptimizelyFeatureEvent).toBeFalsy();
  });

  it('returns useOptimizelyFeatureEvent as false when experiment_flags.experimentFlags is not present', () => {
    (useDecision as jest.Mock).mockReturnValue([
      {
        variables: {},
      },
    ]);

    const { result } = renderHook(() => useOptimizelyFeatureEvent());

    expect(result.current.isOptimizelyFeatureEvent).toBeFalsy();
  });
});
