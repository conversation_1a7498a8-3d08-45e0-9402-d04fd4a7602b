import { useDecision } from '@optimizely/react-sdk';

interface ExperimentFlag {
  experimentFlags: Array<string>;
}

type ReturnVariables = {
  experiment_flags: ExperimentFlag;
};

interface Result {
  isReady: boolean;
  isOptimizelyFeatureEvent: boolean;
  experimentFlags: Array<string>;
}

const FEATURE_FLAG_NAME = 'qantas-hotels-optimizely-feature-event';

const useOptimizelyFeatureEvent = (): Result => {
  const [decision, isReady] = useDecision(FEATURE_FLAG_NAME, {
    autoUpdate: true,
  });

  const variables = decision.variables as ReturnVariables;

  if (!decision || !variables || !variables.experiment_flags) {
    return {
      isReady: false,
      isOptimizelyFeatureEvent: false,
      experimentFlags: [],
    };
  }

  return {
    isReady,
    isOptimizelyFeatureEvent: decision.enabled,
    experimentFlags: variables.experiment_flags.experimentFlags,
  };
};

export default useOptimizelyFeatureEvent;
