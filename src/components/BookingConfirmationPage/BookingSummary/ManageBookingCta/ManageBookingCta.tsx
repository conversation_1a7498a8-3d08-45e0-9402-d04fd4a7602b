import React from 'react';
import { Button, Text } from '@qga/roo-ui/components';

interface ManageBookingCtaProps {
  url: string;
  onClick: () => void;
}

const ManageBookingCta: React.FC<ManageBookingCtaProps> = ({ url, onClick }) => (
  <Button
    as="a"
    data-testid="manage-your-booking"
    href={url}
    variant="primary"
    width={['100%', 'inherit']}
    onClick={onClick}
    target="_blank"
    rel="noopener noreferrer"
    aria-label="Manage your booking (opens in a new tab)"
  >
    <Text fontSize={['sm', 'base']} fontWeight="bold">
      Manage your booking
    </Text>
  </Button>
);

ManageBookingCta.displayName = 'ManageBookingCta';

export default ManageBookingCta;
