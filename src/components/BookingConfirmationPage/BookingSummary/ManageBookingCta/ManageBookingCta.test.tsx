import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ManageBookingCta from './ManageBookingCta';

const TEST_URL = 'https://test-booking.com/manage-booking';

describe('ManageBookingCta', () => {
  it('renders the button with correct label', () => {
    render(<ManageBookingCta url={TEST_URL} onClick={jest.fn()} />);
    expect(screen.getByRole('link', { name: /manage your booking/i })).toBeInTheDocument();
  });

  it('renders with the correct href', () => {
    render(<ManageBookingCta url={TEST_URL} onClick={jest.fn()} />);
    const link = screen.getByRole('link', { name: /manage your booking/i });
    expect(link).toHaveAttribute('href', TEST_URL);
  });

  it('opens link in a new tab with correct rel', () => {
    render(<ManageBookingCta url={TEST_URL} onClick={jest.fn()} />);
    const link = screen.getByRole('link', { name: /manage your booking/i });
    expect(link).toHaveAttribute('target', '_blank');
    expect(link).toHaveAttribute('rel', 'noopener noreferrer');
  });

  it('has the correct aria-label', () => {
    render(<ManageBookingCta url={TEST_URL} onClick={jest.fn()} />);
    const link = screen.getByRole('link', { name: /manage your booking/i });
    expect(link).toHaveAttribute('aria-label', 'Manage your booking (opens in a new tab)');
  });

  it('calls onClick when clicked', async () => {
    const handleClick = jest.fn();
    render(<ManageBookingCta url={TEST_URL} onClick={handleClick} />);
    const link = screen.getByRole('link', { name: /manage your booking/i });
    await userEvent.click(link);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
