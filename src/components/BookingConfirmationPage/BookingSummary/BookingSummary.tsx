import React, { useCallback } from 'react';
import { useSelector } from 'react-redux';
import { Box, Flex, LoadingIndicator } from '@qga/roo-ui/components';
import BookingKnowBeforeYouGo from 'components/BookingConfirmationPage/BookingKnowBeforeYouGo';
import BookingOverview from './BookingOverview';
import StaySummary from './StaySummary';
import ManageBookingCta from './ManageBookingCta';
import BookAnotherRoomCta from './BookAnotherRoomCta';
import { useDataLayer } from 'hooks/useDataLayer';
import { BRAND_SELF_SERVICE_URL, SELF_SERVICE_ENABLED, HOTEL_PROPERTIES_URL } from 'config';
import { useBookAnotherRoomCta } from './hooks/useBookAnotherRoomCta';
import { getProperty, getStayDates, getOccupants, getPointsAmount } from 'store/booking/bookingSelectors';

interface BookingSummaryProps {
  bookingId: string;
  knowBeforeYouGoRef: React.RefObject<{ offsetTop: number }>;
}

const BookingSummary: React.FC<BookingSummaryProps> = ({ bookingId, knowBeforeYouGoRef }) => {
  const occupants = useSelector(getOccupants);
  const property = useSelector(getProperty);
  const stayDates = useSelector(getStayDates);
  const pointsAmount = useSelector(getPointsAmount);
  const payWith = Number(pointsAmount) > 0 ? 'points' : 'cash';
  const { emitInteractionEvent } = useDataLayer();
  const { isReady, showBookAnotherRoomCta } = useBookAnotherRoomCta();
  const handleManageBookingClicked = useCallback(() => {
    emitInteractionEvent({ type: 'Manage Your Booking Button', value: 'Button Selected' });
  }, [emitInteractionEvent]);

  const showBothCtaSection = isReady && showBookAnotherRoomCta && SELF_SERVICE_ENABLED;
  const showManageBookingOnlySection = !showBookAnotherRoomCta && SELF_SERVICE_ENABLED;
  const manageBookingUrl = `${BRAND_SELF_SERVICE_URL}/${bookingId}?source=hotels-booking-confirmation`;
  const bookAnotherRoomUrl = `${HOTEL_PROPERTIES_URL}/${property.id}?adults=${occupants.adults}&checkIn=${stayDates.checkIn}&checkOut=${stayDates.checkOut}&children=${occupants.children}&infants=${occupants.infants}&payWith=${payWith}`;
  return (
    <Flex flexDirection="column" width={1}>
      <BookingOverview />
      <StaySummary />
      {!isReady && <LoadingIndicator color="greys.charcoal" />}
      {showBothCtaSection && (
        <Flex flexDirection={['column', 'row']} gap={2} data-print-style="hidden" mb={8}>
          <ManageBookingCta url={manageBookingUrl} onClick={handleManageBookingClicked} />
          <BookAnotherRoomCta url={bookAnotherRoomUrl} />
        </Flex>
      )}
      <BookingKnowBeforeYouGo knowBeforeYouGoRef={knowBeforeYouGoRef} />
      {showManageBookingOnlySection && (
        <Box data-print-style="hidden">
          <ManageBookingCta url={manageBookingUrl} onClick={handleManageBookingClicked} />
        </Box>
      )}
    </Flex>
  );
};

BookingSummary.displayName = 'BookingSummary';

export default BookingSummary;
