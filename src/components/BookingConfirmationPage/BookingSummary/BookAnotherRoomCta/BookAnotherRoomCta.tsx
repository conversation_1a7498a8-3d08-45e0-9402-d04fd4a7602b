import React from 'react';
import { OutlineButton, Text } from '@qga/roo-ui/components';

interface BookAnotherRoomCtaProps {
  url: string;
}

const BookAnotherRoomCta: React.FC<BookAnotherRoomCtaProps> = ({ url }) => (
  <OutlineButton
    as="a"
    href={url}
    variant="primary"
    width={['100%', 'inherit']}
    target="_blank"
    rel="noopener noreferrer"
    aria-label="Book another room (opens in a new tab)"
  >
    <Text fontSize={['sm', 'base']} fontWeight="bold">
      BOOK ANOTHER ROOM
    </Text>
  </OutlineButton>
);

BookAnotherRoomCta.displayName = 'BookAnotherRoomCta';

export default BookAnotherRoomCta;
