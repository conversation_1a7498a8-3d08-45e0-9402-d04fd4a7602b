import React from 'react';
import { render, screen } from '@testing-library/react';
import BookAnotherRoomCta from './BookAnotherRoomCta';

const TEST_URL = 'https://test-booking.com/another-room';

describe('BookAnotherRoomCta', () => {
  it('renders the button with correct label', () => {
    render(<BookAnotherRoomCta url={TEST_URL} />);
    expect(screen.getByRole('link', { name: /book another room/i })).toBeInTheDocument();
  });

  it('renders with the correct href', () => {
    render(<BookAnotherRoomCta url={TEST_URL} />);
    const link = screen.getByRole('link', { name: /book another room/i });
    expect(link).toHaveAttribute('href', TEST_URL);
  });

  it('opens link in a new tab with correct rel', () => {
    render(<BookAnotherRoomCta url={TEST_URL} />);
    const link = screen.getByRole('link', { name: /book another room/i });
    expect(link).toHaveAttribute('target', '_blank');
    expect(link).toHaveAttribute('rel', 'noopener noreferrer');
  });

  it('has the correct aria-label', () => {
    render(<BookAnotherRoomCta url={TEST_URL} />);
    const link = screen.getByRole('link', { name: /book another room/i });
    expect(link).toHaveAttribute('aria-label', 'Book another room (opens in a new tab)');
  });
});
