import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import BookingSummary from './BookingSummary';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';
import { useDataLayer } from 'hooks/useDataLayer';
import * as config from 'config';
import { mocked } from 'test-utils';
import { useBookAnotherRoomCta } from './hooks/useBookAnotherRoomCta';

jest.mock('./BookingOverview', () => () => <div data-testid="booking-overview">BookingOverview</div>);
jest.mock('./StaySummary', () => () => <div data-testid="stay-summary">StaySummary</div>);
jest.mock('components/BookingConfirmationPage/BookingKnowBeforeYouGo', () => () => (
  <div data-testid="booking-know-before-you-go">BookingKnowBeforeYouGo</div>
));

jest.mock('hooks/useDataLayer');
jest.mock('config');
jest.mock('./hooks/useBookAnotherRoomCta');

jest.mock('store/booking/bookingSelectors', () => ({
  getOccupants: jest.fn(() => ({ adults: 2, children: 1, infants: 0 })),
  getProperty: jest.fn(() => ({ id: 'property-id' })),
  getStayDates: jest.fn(() => ({ checkIn: '2025-01-01', checkOut: '2025-01-03' })),
  getPointsAmount: jest.fn(() => 1000),
}));

const emitInteractionEvent = jest.fn();

type ConfigMock = {
  QH_SELF_SERVICE_URL?: string;
  SELF_SERVICE_ENABLED?: boolean;
  BRAND_SELF_SERVICE_URL?: string;
  HOTEL_PROPERTIES_URL?: string;
};

declare global {
  // eslint-disable-next-line no-var
  var bookingSummaryRef: React.RefObject<{ offsetTop: number }>;
}

const mockStore = configureStore([]);
const initialState = {
  booking: {},
};
const store = mockStore(initialState);

function renderWithProvider(ui: React.ReactElement) {
  return render(<Provider store={store}>{ui}</Provider>);
}

describe('<BookingSummary />', () => {
  beforeEach(() => {
    (useDataLayer as jest.Mock).mockReturnValue({ emitInteractionEvent });
    (config as ConfigMock).QH_SELF_SERVICE_URL = (jest.requireActual('config') as ConfigMock).QH_SELF_SERVICE_URL;
    (config as ConfigMock).SELF_SERVICE_ENABLED = true;
    (config as ConfigMock).BRAND_SELF_SERVICE_URL = 'https://example.com/self-service';
    (config as ConfigMock).HOTEL_PROPERTIES_URL = 'https://example.com/hotels/properties';
    global.bookingSummaryRef = { current: null };
    emitInteractionEvent.mockClear();
    store.clearActions();
    jest.clearAllMocks();
    mocked(useBookAnotherRoomCta).mockReturnValue({ isReady: false, showBookAnotherRoomCta: false });
  });

  it('renders the BookingOverview component', () => {
    renderWithProvider(<BookingSummary bookingId="12345" knowBeforeYouGoRef={global.bookingSummaryRef} />);
    expect(screen.getByTestId('booking-overview')).toBeInTheDocument();
  });

  it('renders the StaySummary component', () => {
    renderWithProvider(<BookingSummary bookingId="12345" knowBeforeYouGoRef={global.bookingSummaryRef} />);
    expect(screen.getByTestId('stay-summary')).toBeInTheDocument();
  });

  it('renders the BookingKnowBeforeYouGo component', () => {
    renderWithProvider(<BookingSummary bookingId="12345" knowBeforeYouGoRef={global.bookingSummaryRef} />);
    expect(screen.getByTestId('booking-know-before-you-go')).toBeInTheDocument();
  });

  it('renders the "Manage your Booking" button with a booking id link', () => {
    renderWithProvider(<BookingSummary bookingId="12345" knowBeforeYouGoRef={global.bookingSummaryRef} />);
    const manageBookingLink = screen.getByRole('link', { name: /manage your booking \(opens in a new tab\)/i });
    expect(manageBookingLink).toBeInTheDocument();
    expect(manageBookingLink).toHaveAttribute(
      'href',
      `${(config as ConfigMock).BRAND_SELF_SERVICE_URL}/12345?source=hotels-booking-confirmation`,
    );
    expect(manageBookingLink).toHaveAttribute('target', '_blank');
    expect(manageBookingLink).toHaveAttribute('rel', 'noopener noreferrer');
  });

  it('dispatches an event to the data layer when "Manage your Booking" button is clicked', async () => {
    renderWithProvider(<BookingSummary bookingId="12345" knowBeforeYouGoRef={global.bookingSummaryRef} />);
    const manageBookingLink = screen.getByRole('link', { name: /manage your booking \(opens in a new tab\)/i });
    await userEvent.click(manageBookingLink);
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Manage Your Booking Button',
      value: 'Button Selected',
    });
  });

  it('does NOT render the "Manage your Booking" button when SELF_SERVICE_ENABLED is false', () => {
    (config as ConfigMock).SELF_SERVICE_ENABLED = false;
    renderWithProvider(<BookingSummary bookingId="12345" knowBeforeYouGoRef={global.bookingSummaryRef} />);
    expect(screen.queryByRole('link', { name: /manage your booking \(opens in a new tab\)/i })).not.toBeInTheDocument();
  });

  it('renders correctly when bookingId prop is an empty string', () => {
    renderWithProvider(<BookingSummary bookingId="" knowBeforeYouGoRef={global.bookingSummaryRef} />);
    const manageBookingLink = screen.getByRole('link', { name: /manage your booking \(opens in a new tab\)/i });
    expect(manageBookingLink).toHaveAttribute(
      'href',
      `${(config as ConfigMock).BRAND_SELF_SERVICE_URL}/?source=hotels-booking-confirmation`,
    );
  });

  it('renders the "Book another room" CTA when showBookAnotherRoomCta is true', () => {
    mocked(useBookAnotherRoomCta).mockReturnValue({ isReady: true, showBookAnotherRoomCta: true });
    renderWithProvider(<BookingSummary bookingId="12345" knowBeforeYouGoRef={global.bookingSummaryRef} />);
    expect(screen.getByRole('link', { name: /book another room/i })).toBeInTheDocument();
    const bookAnotherRoomLink = screen.getByRole('link', { name: /book another room/i });
    expect(bookAnotherRoomLink).toHaveAttribute(
      'href',
      `${(config as ConfigMock).HOTEL_PROPERTIES_URL}/property-id?adults=2&checkIn=2025-01-01&checkOut=2025-01-03&children=1&infants=0&payWith=points`,
    );
  });

  it('does NOT render the "Book another room" CTA when showBookAnotherRoomCta is false', () => {
    mocked(useBookAnotherRoomCta).mockReturnValue({ isReady: true, showBookAnotherRoomCta: false });
    renderWithProvider(<BookingSummary bookingId="12345" knowBeforeYouGoRef={global.bookingSummaryRef} />);
    expect(screen.queryByRole('link', { name: /book another room/i })).not.toBeInTheDocument();
  });
});
