import { renderHook } from '@testing-library/react-hooks';
import { useDecision } from '@optimizely/react-sdk';
import useBookAnotherRoomCta from './useBookAnotherRoomCta';

jest.mock('@optimizely/react-sdk', () => ({
  useDecision: jest.fn(),
}));

describe('useBookAnotherRoomCta', () => {
  beforeEach(() => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: false }, false]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('returns default state when optimizely is not ready', () => {
    const { result } = renderHook(() => useBookAnotherRoomCta());

    expect(result.current).toEqual({
      isReady: false,
      showBookAnotherRoomCta: false,
    });
  });

  it('returns showBookAnotherRoomCta as true when feature flag is on and variation is show_book_another_room_cta', () => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: true, variationKey: 'show_book_another_room_cta' }, true]);

    const { result } = renderHook(() => useBookAnotherRoomCta());

    expect(result.current.showBookAnotherRoomCta).toBeTruthy();
  });

  it('returns showBookAnotherRoomCta as false when feature flag is off', () => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: false }, true]);

    const { result } = renderHook(() => useBookAnotherRoomCta());

    expect(result.current.showBookAnotherRoomCta).toBeFalsy();
  });

  it('returns showBookAnotherRoomCta as false when feature flag is on but variation is not show_book_another_room_cta', () => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: true, variationKey: 'off' }, true]);

    const { result } = renderHook(() => useBookAnotherRoomCta());

    expect(result.current.showBookAnotherRoomCta).toBeFalsy();
  });
});
