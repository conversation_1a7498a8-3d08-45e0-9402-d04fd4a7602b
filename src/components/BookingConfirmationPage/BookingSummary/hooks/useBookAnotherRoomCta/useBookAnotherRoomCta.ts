import { useDecision } from '@optimizely/react-sdk';

const FEATURE_FLAG_NAME = 'qantas-hotels-expt_5-book_another_room_cta';
const VARIATION_KEY = 'show_book_another_room_cta';

type Result = {
  isReady: boolean;
  showBookAnotherRoomCta: boolean;
};

const useBookAnotherRoomCta = (): Result => {
  const [decision, isReady] = useDecision(FEATURE_FLAG_NAME, { autoUpdate: true });

  return {
    isReady,
    showBookAnotherRoomCta: decision.enabled && decision.variationKey === VARIATION_KEY,
  };
};

export default useBookAnotherRoomCta;
