import React from 'react';
import styled from '@emotion/styled';
import { Text as RooText } from '@qga/roo-ui/components';
import Currency from 'components/Currency';

const Text = styled(RooText)`
  white-space: nowrap;
`;

type PointsValue = {
  amount?: string | number;
  currency?: string;
};

interface Props {
  discount?: PointsValue;
  roundToCeiling?: boolean;
}

const PointsStrikethroughMessage = ({ discount = {}, roundToCeiling = false, ...rest }: Props) => {
  return (
    <Text {...rest} data-testid="points-strikethrough">
      &nbsp; (Save{' '}
      <Currency
        amount={discount.amount}
        currency={discount.currency}
        roundToCeiling={roundToCeiling}
        {...rest}
        data-testid="currency-savings"
      />
      )
    </Text>
  );
};

export default PointsStrikethroughMessage;
