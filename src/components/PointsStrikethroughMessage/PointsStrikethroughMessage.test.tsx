import React from 'react';
import { screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import PointsStrikethroughMessage from './index';
import { getPointsStrikethroughMessage } from 'store/campaign/campaignSelectors';
import { renderWithProviders } from 'test-utils/reactUtils';

jest.mock('store/campaign/campaignSelectors');

const mockedGetPointsStrikethroughMessage = getPointsStrikethroughMessage as unknown as jest.Mock;
describe('<PointsStrikethroughMessage />', () => {
  const defaultProps = {
    total: {
      amount: '42000',
      currency: 'PTS',
    },
    discount: {
      amount: '1000',
      currency: 'PTS',
    },
    roundToCeiling: false,
    hideCurrency: false,
    offerType: 'not-classic',
  };

  const renderComponent = () => {
    renderWithProviders(<PointsStrikethroughMessage {...defaultProps} />);
  };
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when the render check is', () => {
    beforeEach(() => {
      mockedGetPointsStrikethroughMessage.mockReturnValue(undefined);
    });

    it('does not render the strikethrough message', () => {
      renderComponent();

      screen.getAllByTestId('points-strikethrough').forEach((node) => {
        expect(node).toHaveTextContent('Save 1,000PTS');
      });
    });
  });
});
