/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useSelector } from 'react-redux';
import { renderWithProviders } from 'test-utils/reactUtils';
import { mocked } from 'test-utils';
import SearchResultDetail from './SearchResultDetail';
import { getQueryCheckIn, getQueryCheckOut } from 'store/router/routerSelectors';
import { getCampaignDefaultSash } from 'store/campaign/campaignSelectors';
import { useShowFullPointsToggle } from './hooks/useShowFullPointsToggle';
import { useShowRedSashToggle } from 'hooks/useShowRedSashToggle';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough';
import { useBreakpoints } from 'hooks/useBreakpoints';
import type { CustomerRating } from 'types/property';
import usePointsRedemptionStrikethrough from 'hooks/optimizely/usePointsRedemptionStrikethrough';

jest.mock('config', () => ({
  SEARCH_DATE_FORMAT: 'yyyy-MM-dd',
  POINTS_EARN_ENABLED: true,
  POINTS_PER_DOLLAR_DEFAULT: 1,
}));

jest.mock('store/router/routerSelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('store/campaign/campaignSelectors');
jest.mock('./hooks/useShowFullPointsToggle');
jest.mock('hooks/optimizely/useAvailableRoomsMessage');
jest.mock('hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough');

jest.mock('./hooks/useShowFullPointsToggle');
jest.mock('hooks/useShowRedSashToggle');
jest.mock('hooks/optimizely/usePointsRedemptionStrikethrough', () => ({
  __esModule: true,
  default: jest.fn(),
}));
jest.mock('../../PropertyPage/PropertyAvailability/RoomTypes/RoomTypeList/Offer/components/OfferDepositPayMessage', () => {
  return function MockOfferDepositPayMessage() {
    return <div data-testid="deposit-pay-message">Deposit Pay Message</div>;
  };
});

jest.mock('components/PointsRedemptionSash', () => {
  return function MockPointsRedemptionSash({ campaignMessage, type, backgroundColor, color }) {
    return (
      <div data-testid="points-redemption-sash" style={{ backgroundColor, color }}>
        {campaignMessage}
      </div>
    );
  };
});

const mockedUseBreakpoints = mocked(useBreakpoints);

const filterDomProps = (props: Record<string, unknown>, additionalStyleProps: string[] = []) => {
  const defaultStylingProps: string[] = [
    'color',
    'pt',
    'pb',
    'pl',
    'pr',
    'p',
    'm',
    'mt',
    'ml',
    'mr',
    'mb',
    'fontSize',
    'lineHeight',
    'display',
  ];
  const allStylingProps = [...defaultStylingProps, ...additionalStyleProps];

  const filteredProps: Record<string, unknown> = { ...props };
  allStylingProps.forEach((prop) => {
    delete filteredProps[prop];
  });

  return filteredProps;
};

const createMockComponent = (testId) => {
  return function MockComponent({ children, ...props }) {
    const validDomProps = {};
    const validAttributes = ['id', 'className', 'style', 'data-testid', 'aria-label', 'role', 'tabIndex'];

    validAttributes.forEach((attr) => {
      if (props[attr] !== undefined) {
        validDomProps[attr] = props[attr];
      }
    });

    if (!validDomProps['data-testid']) {
      validDomProps['data-testid'] = testId;
    }

    return <div {...validDomProps}>{children}</div>;
  };
};

const createHeadingComponent = (tag, testId) => {
  return ({ children, ...props }) => {
    const domProps = filterDomProps(props);
    const HeadingTag = tag;
    return (
      <HeadingTag data-testid={testId} {...domProps}>
        {children}
      </HeadingTag>
    );
  };
};

jest.mock('@qga/roo-ui/components', () => {
  const MockHeading = createHeadingComponent('h2', 'roo-ui-heading');

  ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].forEach((tag) => {
    MockHeading[tag] = createHeadingComponent(tag, `roo-ui-heading-${tag}`);
  });

  return {
    Flex: ({ children, flex, flexDirection, justifyContent, alignItems, flexBasis, ...restProps }) => {
      const domProps = filterDomProps(restProps);
      return (
        <div data-testid="roo-ui-flex" {...domProps}>
          {children}
        </div>
      );
    },
    Box: ({ children, flexBasis, borderRadius, overflow, ...restProps }) => {
      const domProps = filterDomProps(restProps);
      return (
        <div data-testid="roo-ui-box" {...domProps}>
          {children}
        </div>
      );
    },
    Heading: MockHeading,
    Icon: ({ name, size, color, ml, 'data-testid': dataTestId, ...restProps }) => {
      const domProps = filterDomProps(restProps);
      return <span data-testid={dataTestId || `icon-${name}`} {...domProps} />;
    },
    Text: ({ children, color, lineHeight, fontSize, display, mb, 'data-testid': dataTestId, ...restProps }) => {
      const domProps = filterDomProps(restProps);
      return (
        <span data-testid={dataTestId || 'roo-ui-text'} {...domProps}>
          {children}
        </span>
      );
    },
    ImageGallery: ({ images, onImageChanged, showFullscreenButton, height, ...restProps }) => {
      const domProps = filterDomProps(restProps);
      return (
        <div
          data-testid="SearchImageGallery"
          data-images={JSON.stringify(images)}
          data-height={height}
          data-show-fullscreen={showFullscreenButton}
          {...domProps}
        />
      );
    },
    Badge: ({ text, ...props }) => {
      return (
        <span data-testid="mock-badge" {...props}>
          {text}
        </span>
      );
    },
  };
});

jest.mock('components/Currency', () => {
  return function MockCurrency({ amount, currency, testId, ...props }) {
    const domProps = filterDomProps(props, [
      'roundToCeiling',
      'hideCurrency',
      'alignCurrency',
      'fontSize',
      'color',
      'fontWeight',
      'lineHeight',
      'textDecoration',
      'currencyTextFontSize',
      'currencyTextSuffix',
      'termsConditionsSymbol',
      'currencySymbolProps',
      'fromPrice',
      'exclusiveOffer',
      'showCurrencySymbol',
      'isMobile',
    ]);
    return <span data-testid={testId || 'Currency'} data-amount={amount} data-currency={currency} {...domProps} />;
  };
});

jest.mock('components/PointsPerDollar', () => createMockComponent('PointsPerDollar'));

jest.mock('components/CampaignPriceMessage', () => {
  return function MockCampaignPriceMessage({ currency, offerType, luxuryInclusions, fallback, ...props }) {
    const domProps = filterDomProps(props, ['luxuryInclusions', 'offerType']);
    return <div data-testid="campaign-price-message" data-currency={currency} data-offer-type={offerType} {...domProps} />;
  };
});
jest.mock('components/PropertyRatings', () => {
  return jest.fn(({ hotelRating, customerRating }: { hotelRating: number; customerRating: CustomerRating | null }) => (
    <div data-testid="mock-property-ratings">
      {hotelRating && <span data-testid="star-rating">Hotel Rating: {hotelRating}</span>}
      {customerRating && <span data-testid="tripadvisor-rating">Customer Rating: {JSON.stringify(customerRating)}</span>}
    </div>
  ));
});
jest.mock('components/Truncate', () => createMockComponent('Truncate'));
jest.mock('../../PromotionalSashNew', () => {
  return function MockPromotionalSashNew({ promotionName, ...props }) {
    const domProps = filterDomProps(props);
    return <div data-testid="promotional-sash" data-promotion-name={promotionName} {...domProps} />;
  };
});
jest.mock('components/CancellationRefundSummary/CancellationRefundSummary', () => createMockComponent('CancellationRefundSummary'));
jest.mock('./SearchResultLink', () => {
  return function MockSearchResultLink({ children, propertyId, cardIndex, ariaLabel, ...props }) {
    const { ...domProps } = props;
    return (
      <a data-testid="SearchResultLink" aria-label={ariaLabel} {...domProps}>
        {children}
      </a>
    );
  };
});
jest.mock('components/Image/ImageFallback', () => {
  return function MockImageFallback({ src, srcSet, alt, height, width, ...props }) {
    const { ...domProps } = props;
    return <div data-testid="ImageFallback" data-src={src} data-srcset={srcSet} {...domProps} />;
  };
});
jest.mock('../Inclusions', () => createMockComponent('search-inclusions'));
jest.mock('components/RoomsAvailabilityMessage', () => createMockComponent('RoomsAvailabilityMessage'));
jest.mock('components/PriceStrikethrough', () => {
  return function MockPriceStrikethrough({ price, asSup, ...props }) {
    const { ...domProps } = props;
    return <div data-testid="price-strikethrough" data-price={JSON.stringify(price)} {...domProps} />;
  };
});

jest.mock('components/PriceBeforeDiscount', () => {
  return function MockPriceBeforeDiscount({ total, discount, showMessage, ...props }) {
    const amount = total?.amount ?? discount?.amount ?? '';
    const domProps = filterDomProps(props, [
      'roundToCeiling',
      'lineHeight',
      'offerType',
      'fontSize',
      'color',
      'fontWeight',
      'textDecoration',
      'hideCurrency',
    ]);
    return (
      <div data-testid="PriceBeforeDiscount" data-amount={amount} {...domProps}>
        {amount}
      </div>
    );
  };
});
jest.mock('components/PointsEarnSummary', () => createMockComponent('PointsEarnSummary'));
jest.mock('components/PromotionalSashRedVariant', () => {
  return function MockPromotionalSashRedVariant({ promotionName, ...props }) {
    const { ...domProps } = props;
    return <div data-testid="promotional-sash-red" data-promotion-name={promotionName} {...domProps} />;
  };
});

jest.mock('hooks/useBreakpoints', () => ({
  useBreakpoints: jest.fn(() => ({ isLessThanBreakpoint: jest.fn((breakpoint) => false) })),
}));

jest.mock('hooks/useDataLayer', () => ({
  useDataLayer: jest.fn(() => ({ emitInteractionEvent: jest.fn() })),
}));

jest.mock('lib/date/numberOfNights', () => ({
  getNumberOfNights: jest.fn(() => 1),
}));

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
}));

const mockedUseSelector = mocked(useSelector);
const mockedUseShowFullPointsToggle = mocked(useShowFullPointsToggle);
const mockedUseShowRedSashToggle = mocked(useShowRedSashToggle);
const mockedUseAvailableRoomsMessage = mocked(useAvailableRoomsMessage);
const mockedUsePriceStrikethrough = mocked(usePriceStrikethrough);
const mockedUsePointsRedemptionStrikethrough = mocked(usePointsRedemptionStrikethrough);

let result;

beforeEach(() => {
  jest.clearAllMocks();

  mockedUseSelector.mockImplementation((selector) => {
    if (selector === getQueryCheckIn) return new Date('2023-01-01');
    if (selector === getQueryCheckOut) return new Date('2023-01-02');
    if (selector === getCampaignDefaultSash) return null;
    return undefined;
  });

  mockedUseShowFullPointsToggle.mockReturnValue({ showFullPoints: false, isReady: true });
  mockedUseShowRedSashToggle.mockReturnValue({ showRedSash: false, isReady: true });
  mockedUseAvailableRoomsMessage.mockReturnValue({ isReady: true, showMessage: false, max_rooms_cutoff: 5 });
  mockedUsePriceStrikethrough.mockReturnValue({ isReady: true, showFullTraffic: false, showVariationB: false });
  mockedUsePointsRedemptionStrikethrough.mockReturnValue({ isReady: true, isPointsRedemptionStrikethrough: true });

  result = {
    property: {
      id: '12345',
      name: 'Test Hotel',
      description: 'A great test hotel',
      category: 'hotel',
      address: {
        streetAddress: '123 Test St',
        suburb: 'Test Suburb',
        state: 'NSW',
        postcode: '2000',
        country: 'Australia',
        countryCode: 'AU',
      },
      images: [
        {
          caption: 'Test Image',
          urlSmall: 'test-small.jpg',
          urlOriginal: 'test.jpg',
          urlMedium: 'test-med.jpg',
          urlLarge: 'test-large.jpg',
        },
      ],
      mainImage: {
        caption: 'Main Test Image',
        urlSmall: 'main-small.jpg',
        urlOriginal: 'main.jpg',
        urlMedium: 'main-med.jpg',
        urlLarge: 'main-large.jpg',
      },
      customerRatings: [
        {
          averageRating: 4.5,
          ratingImageUrl: 'rating.jpg',
          reviewCount: 100,
          source: 'test',
        },
      ],
      roomType: {
        name: 'Standard Room',
        id: 'room1',
        maxOccupantCount: 2,
        offer: [],
        images: [],
        mainImage: {
          caption: 'Room Image',
          urlSmall: 'room-small.jpg',
          urlOriginal: 'room.jpg',
          urlMedium: 'room-med.jpg',
          urlLarge: 'room-large.jpg',
        },
        RoomTypeFacilities: {},
      },
      roomTypes: [],
      ratingType: 'AAA' as const,
      rating: 4,
      promotionSashes: [],
    },
    offer: {
      type: 'standard',
      charges: {
        total: { currency: 'AUD', amount: '200' },
        totalBeforeDiscount: { currency: 'AUD', amount: '250' },
        totalDiscount: { currency: 'AUD', amount: '50' },
        taxes: { currency: 'AUD', amount: '20' },
        strikethrough: {
          price: { currency: 'AUD', amount: '250' },
        },
      },
      supplier: { name: 'Test Supplier' },
      inclusions: [],
      luxOffer: false,
      cancellationPolicy: {
        isNonrefundable: false,
        description: 'Free cancellation',
        cancellationWindows: [],
      },
      promotion: null,
      pointsEarned: {
        qffPoints: { total: 100 },
        maxQffEarnPpd: 1,
      },
      allocationsAvailable: 5,
      depositPay: { depositPayable: true },
    },
    roomType: {
      name: 'Standard Room',
      maxOccupantCount: 2,
    },
  };
});

const renderComponent = (cardIndex = 0) => {
  return renderWithProviders(<SearchResultDetail result={result} cardIndex={cardIndex} />);
};

const expectComponentToThrow = (setupFn) => {
  const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
  setupFn();
  expect(() => renderComponent()).toThrow();
  consoleSpy.mockRestore();
};

describe('SearchResultDetail', () => {
  describe.each([
    {
      currency: 'AUD',
      offerType: 'standard',
      expectedSymbol: 'AUD',
      expectedTestId: 'currency-cash-symbol',
      shouldShowPointsPerDollar: true,
      shouldShowCampaignMessage: false,
    },
    {
      currency: 'PTS',
      offerType: 'special',
      expectedSymbol: 'PTS*',
      expectedTestId: 'points-plus-asterisk',
      shouldShowPointsPerDollar: false,
      shouldShowCampaignMessage: true,
    },
  ])(
    'Currency handling for $currency',
    ({ currency, offerType, expectedSymbol, expectedTestId, shouldShowPointsPerDollar, shouldShowCampaignMessage }) => {
      beforeEach(() => {
        result.offer.type = offerType;
        result.offer.charges.total.currency = currency;
      });

      it(`should display ${currency} currency symbol`, () => {
        renderComponent();
        expect(screen.getByTestId(expectedTestId)).toBeInTheDocument();
        expect(screen.getByTestId(expectedTestId)).toHaveTextContent(expectedSymbol);
      });

      it(`should ${shouldShowPointsPerDollar ? '' : 'not '}display PointsPerDollar component`, () => {
        renderComponent();
        const pointsPerDollar = screen.queryByTestId('PointsPerDollar');
        if (shouldShowPointsPerDollar) {
          expect(pointsPerDollar).toBeInTheDocument();
        } else {
          expect(pointsPerDollar).not.toBeInTheDocument();
        }
      });
    },
  );

  it('should display points earn summary when showFullPoints is true', () => {
    mockedUseShowFullPointsToggle.mockReturnValue({ showFullPoints: true, isReady: true });
    renderComponent();
    expect(screen.getByTestId('PointsEarnSummary')).toBeInTheDocument();
  });

  describe.each([
    {
      scenario: 'when result has images',
      setup: () => {
        result.property.images = [
          {
            caption: 'Image 1',
            urlSmall: 'image1-small.jpg',
            urlOriginal: 'image1.jpg',
            urlMedium: 'image1-med.jpg',
            urlLarge: 'image1-large.jpg',
          },
        ];
      },
      expectPresent: ['SearchImageGallery'],
      expectAbsent: [],
    },
    {
      scenario: 'when result has no main image',
      setup: () => {
        result.property.mainImage = null;
      },
      expectPresent: ['ImageFallback'],
      expectAbsent: [],
    },
    {
      scenario: 'when result has rating',
      setup: () => {
        result.property.rating = 4.5;
      },
      expectPresent: ['star-rating'],
      expectAbsent: [],
    },
    {
      scenario: 'when result has no rating',
      setup: () => {
        result.property.rating = 0;
      },
      expectPresent: [],
      expectAbsent: ['star-rating'],
    },
    {
      scenario: 'when result has TripAdvisor ratings',
      setup: () => {
        result.property.customerRatings = [
          {
            averageRating: 4.5,
            ratingImageUrl: 'rating.jpg',
            reviewCount: 100,
            source: 'trip_advisor',
          },
        ];
      },
      expectPresent: ['tripadvisor-rating'],
      expectAbsent: [],
    },
    {
      scenario: 'when result has inclusions',
      setup: () => {
        result.offer.inclusions = [{ code: 'wifi', name: 'Free WiFi' }];
      },
      expectPresent: ['search-inclusions'],
      expectAbsent: [],
    },
    {
      scenario: 'when result has no inclusions',
      setup: () => {
        result.offer.inclusions = [];
      },
      expectPresent: [],
      expectAbsent: ['search-inclusions'],
    },
    {
      scenario: 'when offer is classic type',
      setup: () => {
        result.offer.type = 'classic';
      },
      expectPresent: ['classic-ribbon'],
      expectAbsent: [],
    },
    {
      scenario: 'when result has promotional sash',
      setup: () => {
        result.offer.promotion = { name: 'Special Deal' };
      },
      expectPresent: ['promotional-sash'],
      expectAbsent: [],
    },
  ])('$scenario', ({ setup, expectPresent, expectAbsent }) => {
    it('renders correctly', () => {
      setup();
      renderComponent();

      expectPresent.forEach((testId) => {
        expect(screen.getByTestId(testId)).toBeInTheDocument();
      });

      expectAbsent.forEach((testId) => {
        expect(screen.queryByTestId(testId)).not.toBeInTheDocument();
      });
    });
  });

  it('should render red promotional sash variant when member deal and red sash enabled', () => {
    result.offer.promotion = { name: 'Member Deal' };
    mockedUseShowRedSashToggle.mockReturnValue({ showRedSash: true, isReady: true });
    renderComponent();
    expect(screen.getByTestId('promotional-sash-red')).toBeInTheDocument();
  });
});

describe('when available rooms message is enabled', () => {
  beforeEach(() => {
    result.offer.allocationsAvailable = 3;
    mockedUseAvailableRoomsMessage.mockReturnValue({
      isReady: true,
      showMessage: true,
      max_rooms_cutoff: 5,
    });
  });

  it('should render rooms availability message', () => {
    renderComponent();
    expect(screen.getByTestId('RoomsAvailabilityMessage')).toBeInTheDocument();
  });
});

describe('property information display', () => {
  it('should display property name', () => {
    renderComponent();
    expect(screen.getByTestId('property-name')).toHaveTextContent('Test Hotel');
  });

  it('should display property suburb', () => {
    renderComponent();
    expect(screen.getByTestId('property-suburb')).toHaveTextContent('Test Suburb');
  });

  it('should display number of nights', () => {
    renderComponent();
    expect(screen.getByTestId('number-of-nights')).toHaveTextContent('1 from');
  });

  it('should display PriceStrikethrough when showPriceStrikethrough is true', () => {
    mockedUsePriceStrikethrough.mockReturnValue({ isReady: true, showFullTraffic: true, showVariationB: false });
    result.offer.charges.strikethrough.price = { currency: 'AUD', amount: '250' };
    renderComponent();
    expect(screen.getByTestId('price-strikethrough')).toBeInTheDocument();
  });

  it('should render currency component with correct amount', () => {
    renderComponent();
    const currency = screen.getByTestId('currency');
    expect(currency).toHaveAttribute('data-amount', '200');
    expect(currency).toHaveAttribute('data-currency', 'AUD');
  });
});

describe('PriceBeforeDiscount when isPointsRedemptionStrikethrough is true', () => {
  describe('paywith is points, isLuxuryOffer is false, has discount and offer is not classic', () => {
    it('should render the correct price before discount', () => {
      const modifiedResult = {
        ...result,
        offer: {
          ...result.offer,
          charges: {
            ...result.offer.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
        },
      };
      const cardIndex = 0;
      renderWithProviders(<SearchResultDetail result={modifiedResult} cardIndex={cardIndex} />);
      expect(screen.getByTestId('PriceBeforeDiscount')).toHaveTextContent('2500');
    });
  });

  describe('paywith is points, isLuxuryOffer is false, offer is not classic but there is no discount', () => {
    it('should not render the price before discount', () => {
      const modifiedResult = {
        ...result,
        offer: {
          ...result.offer,
          charges: {
            ...result.offer.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2000', currency: 'PTS' },
            totalDiscount: { amount: '0', currency: 'PTS' },
          },
        },
      };
      const cardIndex = 0;
      renderWithProviders(<SearchResultDetail result={modifiedResult} cardIndex={cardIndex} />);
      expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
    });
  });

  describe('paywith is points, isLuxuryOffer is false, hasDiscount is true and offer type is classic', () => {
    it('should not render the price before discount', () => {
      const modifiedResult = {
        ...result,
        offer: {
          ...result.offer,
          charges: {
            ...result.offer.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
          type: 'classic',
        },
      };
      const cardIndex = 0;
      renderWithProviders(<SearchResultDetail result={modifiedResult} cardIndex={cardIndex} />);
      expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
    });
  });

  describe('paywith is cash, hasDiscount is true, isLuxuryOffer is false, and offer type is not classic', () => {
    it('should not render the price before discount', () => {
      const modifiedResult = {
        ...result,
        offer: {
          ...result.offer,
          charges: {
            ...result.offer.charges,
            total: { amount: '200', currency: 'AUD' },
            totalBeforeDiscount: { amount: '250', currency: 'AUD' },
            totalDiscount: { amount: '50', currency: 'AUD' },
          },
        },
      };
      const cardIndex = 0;
      renderWithProviders(<SearchResultDetail result={modifiedResult} cardIndex={cardIndex} />);
      expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
    });
  });

  describe('paywith is points, hasDiscount is true and offer type is not classic but isLuxuryOffer is true', () => {
    it('should not render the price before discount', () => {
      const modifiedResult = {
        ...result,
        offer: {
          ...result.offer,
          charges: {
            ...result.offer.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
          type: 'standard',
          luxOffer: true,
        },
      };
      const cardIndex = 0;
      renderWithProviders(<SearchResultDetail result={modifiedResult} cardIndex={cardIndex} />);
      expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
    });
  });
  describe('showPointsRedemptionStrikethrough is true', () => {
    it('should render the PointsRedemptionSash', () => {
      const modifiedResult = {
        ...result,
        offer: {
          ...result.offer,
          charges: {
            ...result.offer.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
          type: 'standard',
          luxOffer: false,
        },
      };
      const cardIndex = 0;
      renderWithProviders(<SearchResultDetail result={modifiedResult} cardIndex={cardIndex} />);
      expect(screen.getByTestId('points-redemption-sash')).toBeInTheDocument();
    });
  });
});

describe('PriceBeforeDiscount when isPointsRedemptionStrikethrough is false', () => {
  describe('when paywith is points, hasDiscount is true, isLuxuryOffer is false and offer is not classic', () => {
    beforeEach(() => {
      mockedUsePointsRedemptionStrikethrough.mockReturnValue({ isReady: true, isPointsRedemptionStrikethrough: false });
    });

    it('should not render the price before discount', () => {
      const modifiedResult = {
        ...result,
        offer: {
          ...result.offer,
          charges: {
            ...result.offer.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
        },
      };
      const cardIndex = 0;
      renderWithProviders(<SearchResultDetail result={modifiedResult} cardIndex={cardIndex} />);
      expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
    });
  });

  it('renders the CampaignPriceMessage with the correct props', () => {
    mockedUsePointsRedemptionStrikethrough.mockReturnValue({ isReady: true, isPointsRedemptionStrikethrough: false });
    const modifiedResult = {
      ...result,
      offer: {
        ...result.offer,
        charges: {
          ...result.offer.charges,
          total: { amount: '2000', currency: 'PTS' },
          totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
          totalDiscount: { amount: '500', currency: 'PTS' },
        },
      },
    };
    const cardIndex = 0;

    renderWithProviders(<SearchResultDetail result={modifiedResult} cardIndex={cardIndex} />);
    expect(screen.getByTestId('campaign-price-message')).toBeInTheDocument();
    expect(screen.getByTestId('campaign-price-message')).toHaveAttribute('data-currency', 'PTS');
    expect(screen.getByTestId('campaign-price-message')).toHaveAttribute('data-offer-type', 'standard');
  });
});

describe('cancellation policy', () => {
  it('should render cancellation refund summary', () => {
    renderComponent();
    expect(screen.getByTestId('CancellationRefundSummary')).toBeInTheDocument();
  });
});

describe('OfferDepositPayMessage', () => {
  it('should render deposit pay message when isDepositPay is true', () => {
    renderComponent();
    expect(screen.getByTestId('deposit-pay-message')).toBeInTheDocument();
  });

  it('should not render deposit pay message when isDepositPay is false', () => {
    result.offer.depositPay.depositPayable = false;
    renderComponent();
    expect(screen.queryByTestId('deposit-pay-message')).not.toBeInTheDocument();
  });
});

describe('search result link', () => {
  it('should render search result link with correct aria label', () => {
    renderComponent();
    const link = screen.getByTestId('SearchResultLink');
    expect(link).toHaveAttribute('aria-label', 'Test Hotel from 200 dollars');
  });

  it('should render search result link with points aria label when currency is PTS', () => {
    result.offer.charges.total.currency = 'PTS';
    renderComponent();
    const link = screen.getByTestId('SearchResultLink');
    expect(link).toHaveAttribute('aria-label', 'Test Hotel from 200 points');
  });
});

describe.each([{ cardIndex: 0 }, { cardIndex: 5 }])('component rendering with card index $cardIndex', ({ cardIndex }) => {
  it('should render without errors', () => {
    renderComponent(cardIndex);
    expect(screen.getByTestId('property-name')).toBeInTheDocument();
  });
});

describe('responsive behavior', () => {
  it('should handle mobile breakpoint', () => {
    mockedUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn((breakpoint) => breakpoint === 0),
    });

    result.offer.inclusions = [{ code: 'wifi', name: 'Free WiFi' }];
    renderComponent();

    expect(screen.getByTestId('SearchImageGallery')).toHaveAttribute('data-height', '144px');

    const inclusions = screen.getAllByTestId('search-inclusions');
    expect(inclusions).toHaveLength(1);
  });

  it('should handle desktop breakpoint', () => {
    mockedUseBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn(() => false),
    });

    result.offer.inclusions = [{ code: 'wifi', name: 'Free WiFi' }];
    renderComponent();

    expect(screen.getByTestId('SearchImageGallery')).not.toHaveAttribute('data-height', '144px');

    const inclusions = screen.getAllByTestId('search-inclusions');
    expect(inclusions).toHaveLength(1);
  });
});

describe('error handling', () => {
  it('should handle missing property data gracefully', () => {
    result.property.name = '';
    result.property.address.suburb = '';
    renderComponent();
    expect(screen.getByTestId('property-name')).toHaveTextContent('');
    expect(screen.getByTestId('property-suburb')).toHaveTextContent('');
  });

  it('should handle missing offer data gracefully', () => {
    result.offer.charges.total.amount = '';
    renderComponent();
    const currency = screen.getByTestId('currency');
    expect(currency).toHaveAttribute('data-amount', '');
  });

  describe.each([
    {
      description: 'missing property.address',
      setup: () => {
        result.property.address = null;
      },
      assertion: () => {
        renderComponent();
        expect(screen.getByTestId('property-suburb')).toHaveTextContent('');
      },
    },
    {
      description: 'missing property.id',
      setup: () => {
        result.property.id = null;
      },
      assertion: () => {
        renderComponent();
        expect(screen.getByTestId('SearchResultLink')).toBeInTheDocument();
      },
    },
    {
      description: 'missing offer.charges',
      setup: () => {
        result.offer.charges = null;
      },
      assertion: () => expectComponentToThrow(() => {}),
    },
    {
      description: 'missing offer.charges.total',
      setup: () => {
        result.offer.charges.total = null;
      },
      assertion: () => expectComponentToThrow(() => {}),
    },
    {
      description: 'undefined currency',
      setup: () => {
        result.offer.charges.total.currency = undefined;
      },
      assertion: () => {
        renderComponent();
        expect(screen.getByTestId('property-name')).toBeInTheDocument();
      },
    },
  ])('should handle $description gracefully', ({ setup, assertion }) => {
    it('renders without errors', () => {
      setup();
      assertion();
    });
  });

  describe.each([
    {
      description: 'missing property.mainImage',
      setup: () => {
        result.property.mainImage = null;
      },
      expectPresent: ['ImageFallback'],
      expectAbsent: ['SearchImageGallery'],
    },
    {
      description: 'missing property.customerRatings',
      setup: () => {
        result.property.customerRatings = null;
      },
      expectPresent: [],
      expectAbsent: ['tripadvisor-rating'],
    },
    {
      description: 'empty property.customerRatings array',
      setup: () => {
        result.property.customerRatings = [];
      },
      expectPresent: [],
      expectAbsent: ['tripadvisor-rating'],
    },
    {
      description: 'missing offer.promotion',
      setup: () => {
        result.offer.promotion = null;
      },
      expectPresent: [],
      expectAbsent: ['promotional-sash', 'promotional-sash-red'],
    },
    {
      description: 'missing offer.allocationsAvailable',
      setup: () => {
        delete result.offer.allocationsAvailable;
      },
      expectPresent: [],
      expectAbsent: ['RoomsAvailabilityMessage'],
    },
    {
      description: 'missing offer.charges.strikethrough',
      setup: () => {
        result.offer.charges.strikethrough = null;
      },
      expectPresent: [],
      expectAbsent: ['PriceStrikethrough'],
    },
  ])('should handle $description gracefully', ({ setup, expectPresent, expectAbsent }) => {
    it('renders correctly', () => {
      setup();
      renderComponent();

      expectPresent.forEach((testId) => {
        expect(screen.getByTestId(testId)).toBeInTheDocument();
      });

      expectAbsent.forEach((testId) => {
        expect(screen.queryByTestId(testId)).not.toBeInTheDocument();
      });
    });
  });

  describe.each([
    {
      description: 'missing offer.pointsEarned',
      setup: () => {
        result.offer.pointsEarned = null;
      },
      expectComponent: 'PointsPerDollar',
    },
    {
      description: 'missing offer.pointsEarned.maxQffEarnPpd',
      setup: () => {
        result.offer.pointsEarned.maxQffEarnPpd = null;
      },
      expectComponent: 'PointsPerDollar',
    },
    {
      description: 'missing offer.cancellationPolicy',
      setup: () => {
        result.offer.cancellationPolicy = null;
      },
      expectComponent: 'CancellationRefundSummary',
    },
  ])('should handle $description gracefully', ({ setup, expectComponent }) => {
    it('still renders required components', () => {
      setup();
      renderComponent();
      expect(screen.getByTestId(expectComponent)).toBeInTheDocument();
    });
  });

  it.each([
    {
      description: 'invalid property.rating values',
      setup: () => {
        result.property.rating = -1;
      },
      expectPresent: 'star-rating',
    },
    {
      description: 'zero property.rating',
      setup: () => {
        result.property.rating = 0;
      },
      expectAbsent: 'star-rating',
    },
    {
      description: 'missing property.ratingType',
      setup: () => {
        result.property.ratingType = null;
      },
      expectPresent: 'property-name',
    },
    {
      description: 'empty property.images array',
      setup: () => {
        result.property.images = [];
      },
      expectPresent: 'property-name',
    },
  ])('should handle $description gracefully', ({ setup, expectPresent, expectAbsent }) => {
    setup();
    renderComponent();

    if (expectPresent) {
      expect(screen.getByTestId(expectPresent)).toBeInTheDocument();
    }

    if (expectAbsent) {
      expect(screen.queryByTestId(expectAbsent)).not.toBeInTheDocument();
    }
  });

  describe('hook failure states', () => {
    it('should handle hooks not being ready gracefully', () => {
      mockedUseShowFullPointsToggle.mockReturnValue({ showFullPoints: false, isReady: false });
      mockedUseShowRedSashToggle.mockReturnValue({ showRedSash: false, isReady: false });
      mockedUseAvailableRoomsMessage.mockReturnValue({ isReady: false, showMessage: false, max_rooms_cutoff: 5 });
      mockedUsePriceStrikethrough.mockReturnValue({ isReady: false, showFullTraffic: false, showVariationB: false });

      renderComponent();
      expect(screen.getByTestId('property-name')).toBeInTheDocument();
    });

    it('should handle selector returning undefined values gracefully', () => {
      mockedUseSelector.mockImplementation(() => undefined);
      renderComponent();
      expect(screen.getByTestId('property-name')).toBeInTheDocument();
    });
  });
});
