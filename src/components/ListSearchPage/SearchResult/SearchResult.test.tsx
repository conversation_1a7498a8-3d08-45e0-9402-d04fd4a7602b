import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { ThemeProvider } from 'emotion-theming';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { configureStore } from 'redux-mock-store';
import SearchResult from './SearchResult';
import { mocked } from 'test-utils';
import {
  getQueryFeaturedPropertyId,
  getQueryUtmSource,
  getQueryUtmCampaign,
  getQueryPage,
  getQueryLocation,
} from 'store/router/routerSelectors';
import { getPropertyLinkQueryString } from 'store/search/searchSelectors';
import { getCampaignDefaultSash } from 'store/campaign/campaignSelectors';
import * as sessionStorage from 'lib/browser/sessionStorage';
import '@testing-library/jest-dom';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough';

const mockStore = configureStore([]);

jest.mock('config', () => ({
  SEARCH_DATE_FORMAT: 'yyyy-MM-dd',
  LIST_SEARCH_LIMIT: 10,
}));

jest.mock('store/router/routerSelectors');
jest.mock('store/search/searchSelectors');
jest.mock('store/campaign/campaignSelectors');

jest.mock('./SearchResultDetail', () => {
  return function MockSearchResultDetail() {
    return <div data-testid="search-result-detail">SearchResultDetail</div>;
  };
});

jest.mock('lib/browser/sessionStorage', () => ({
  set: jest.fn(),
}));

jest.mock('hooks/useBreakpoints', () => ({
  useBreakpoints: () => ({
    isLessThanBreakpoint: jest.fn(() => false),
  }),
}));

jest.mock('hooks/useViewPromotionEvent', () => jest.fn());

const mockFireSelectItemEvent = jest.fn();
jest.mock('hooks/useSelectItemEvent', () => () => ({
  fireSelectItemEvent: mockFireSelectItemEvent,
}));

jest.mock('hooks/useSelectPromotionEvent', () => () => ({
  fireSelectPromotionEvent: jest.fn(),
}));

jest.mock('components/RoomsAvailabilityMessage/getRoomAvailabilityMessage', () => ({
  getRoomAvailabilityMessageStandard: jest.fn(() => 'Only 2 rooms left'),
}));

jest.mock('lib/analytics/eventsMap/helpers/GetShowCtaMessage', () => ({
  GetShowCtaMessage: jest.fn(() => true),
}));

jest.mock('hooks/optimizely/usePriceStrikethrough', () => jest.fn());

const mockTheme = {
  colors: {
    white: '#ffffff',
    greys: {
      charcoal: '#333333',
    },
    brand: {
      secondary: '#ff6600',
    },
    ui: {
      badgeText: '#000000',
      badgeBackground: '#f0f0f0',
    },
    external: {
      trivago: '#ff6600',
      tripAdvisor: '#00aa00',
      google: '#4285f4',
    },
  },
  space: [0, 4, 8, 16, 32],
  radii: {
    default: '4px',
    defaultRoundTopOnly: '4px 4px 0 0',
  },
  shadows: {
    heavy: '0 4px 8px rgba(0,0,0,0.1)',
  },
  zIndices: {
    highlightBorder: 1,
  },
};

let result;
const propertyQueryString = 'propertyQueryString';
const index = 1;
const page = 2;

const renderWithProviders = (component, { initialState = {} } = {}) => {
  const store = mockStore(initialState);
  return render(
    <Provider store={store}>
      <ThemeProvider theme={mockTheme}>
        <BrowserRouter>{component}</BrowserRouter>
      </ThemeProvider>
    </Provider>,
  );
};

beforeEach(() => {
  mocked(getQueryPage).mockReturnValue(page);
  mocked(getPropertyLinkQueryString).mockReturnValue(propertyQueryString);
  mocked(getQueryLocation).mockReturnValue('Sydney');
  mocked(getCampaignDefaultSash).mockReturnValue(null);

  result = {
    property: {
      id: '111',
      name: 'Hilton',
    },
    offer: {
      charges: {
        total: {
          amount: 100,
          currency: 'AUD',
        },
        strikethrough: {
          amount: 120,
          currency: 'AUD',
        },
      },
      type: 'offer',
      allocationsAvailable: 3,
      promotion: {
        name: 'Summer Sale',
      },
    },
    roomType: {
      id: 'deluxe',
      name: 'Deluxe Room',
    },
  };

  mockFireSelectItemEvent.mockClear();
  mocked(usePriceStrikethrough).mockReturnValue({ showFullTraffic: false, showVariationB: false });
});

afterEach(() => {
  mocked(getQueryUtmCampaign).mockReset();
  mocked(getQueryUtmSource).mockReset();
  mocked(getQueryFeaturedPropertyId).mockReset();
  mocked(getQueryLocation).mockReset();
  mocked(getCampaignDefaultSash).mockReset();
});

const renderComponent = () => renderWithProviders(<SearchResult result={result} index={index} />);

describe('SearchResult', () => {
  describe('basic rendering', () => {
    it('renders the search result card', () => {
      renderComponent();
      expect(screen.getByTestId('search-result')).toBeInTheDocument();
    });

    it('renders the SearchResultDetail component', () => {
      renderComponent();
      expect(screen.getByTestId('search-result-detail')).toBeInTheDocument();
    });
  });

  describe('interactions', () => {
    it('calls event handlers when clicked', async () => {
      renderComponent();

      const searchResult = screen.getByTestId('search-result');
      await userEvent.click(searchResult);

      expect(sessionStorage.set).toHaveBeenCalledWith('scrolledHeight', 0);
      expect(mockFireSelectItemEvent).toHaveBeenCalledTimes(1);
    });
  });

  describe('highlighting logic', () => {
    describe('property matching', () => {
      it.each([
        ['string IDs', '111', '111'],
        ['numeric IDs', 111, 111],
        ['mixed type IDs', '111', 111],
      ])('shows highlight for matching %s', (_, propertyId, featuredId) => {
        result.property.id = propertyId;
        mocked(getQueryFeaturedPropertyId).mockReturnValue(featuredId);
        renderComponent();
        expect(screen.getByTestId('search-result-heading')).toBeInTheDocument();
      });

      it.each([
        ['null featured property ID', null],
        ['undefined featured property ID', undefined],
      ])('hides highlight when %s', (_, featuredId) => {
        mocked(getQueryFeaturedPropertyId).mockReturnValue(featuredId);
        renderComponent();
        expect(screen.queryByTestId('search-result-heading')).not.toBeInTheDocument();
      });

      it('hides highlight when property ID is null', () => {
        result.property.id = null;
        mocked(getQueryFeaturedPropertyId).mockReturnValue('111');
        renderComponent();
        expect(screen.queryByTestId('search-result-heading')).not.toBeInTheDocument();
      });
    });

    describe('when property is featured', () => {
      beforeEach(() => {
        mocked(getQueryFeaturedPropertyId).mockReturnValue(result.property.id);
      });

      describe('UTM referrer handling', () => {
        it('prioritizes UTM campaign over UTM source', () => {
          mocked(getQueryUtmCampaign).mockReturnValue('TripAdvisor');
          mocked(getQueryUtmSource).mockReturnValue('Trivago');
          renderComponent();
          expect(screen.getByTestId('search-result-heading')).toHaveTextContent('You picked this hotel on TripAdvisor');
        });

        it.each([
          ['null', null],
          ['undefined', undefined],
          ['empty string', ''],
        ])('falls back to UTM source when UTM campaign is %s', (_, campaignValue) => {
          mocked(getQueryUtmCampaign).mockReturnValue(campaignValue);
          mocked(getQueryUtmSource).mockReturnValue('TripAdvisor');
          renderComponent();
          expect(screen.getByTestId('search-result-heading')).toHaveTextContent('You picked this hotel on TripAdvisor');
        });

        it('uses invalid UTM campaign value as-is (falls back to default)', () => {
          mocked(getQueryUtmCampaign).mockReturnValue('invalid');
          mocked(getQueryUtmSource).mockReturnValue('TripAdvisor');
          renderComponent();
          expect(screen.getByTestId('search-result-heading')).toHaveTextContent('You picked this hotel');
        });
      });

      describe('valid referrer sources', () => {
        it.each([
          ['Trivago', 'on Trivago'],
          ['GoogleHotels', 'on Google'],
          ['Email', 'from your Email'],
          ['TripAdvisor', 'on TripAdvisor'],
        ])('handles %s referrer', (referrer, expectedText) => {
          mocked(getQueryUtmCampaign).mockReturnValue(referrer);
          renderComponent();
          expect(screen.getByTestId('search-result-heading')).toHaveTextContent(`You picked this hotel ${expectedText}`);
        });
      });

      describe('multiple UTM values', () => {
        it.each([
          ['utmCampaigns', () => mocked(getQueryUtmCampaign)],
          ['utmSources', () => mocked(getQueryUtmSource)],
        ])('picks first valid referrer from multiple %s', (paramType, getMockFn) => {
          getMockFn().mockReturnValue(['invalidCampaign', 'TripAdvisor']);
          renderComponent();
          expect(screen.getByTestId('search-result-heading')).toHaveTextContent('You picked this hotel on TripAdvisor');
        });
      });

      describe('fallback scenarios', () => {
        it.each([
          ['unrecognized UTM campaign', () => mocked(getQueryUtmCampaign), 'unrecognizedValue'],
          ['unrecognized UTM source', () => mocked(getQueryUtmSource), 'unrecognizedValue'],
          [
            'empty UTM values',
            () => {
              mocked(getQueryUtmCampaign).mockReturnValue('');
              return mocked(getQueryUtmSource);
            },
            '',
          ],
          [
            'null UTM values',
            () => {
              mocked(getQueryUtmCampaign).mockReturnValue(null);
              return mocked(getQueryUtmSource);
            },
            null,
          ],
        ])('falls back to default text with %s', (_, setupMock, value) => {
          setupMock().mockReturnValue(value);
          renderComponent();
          expect(screen.getByTestId('search-result-heading')).toHaveTextContent('You picked this hotel');
        });
      });
    });

    describe('when property is not featured', () => {
      it('does not render highlight component', () => {
        renderComponent();
        expect(screen.queryByTestId('search-result-heading')).not.toBeInTheDocument();
      });
    });
  });

  describe('offer type handling', () => {
    it.each([
      ['classic', 'classic'],
      ['non-classic', 'offer'],
    ])('handles %s offer type', (_, offerType) => {
      result.offer.type = offerType;
      renderComponent();
      expect(screen.getByTestId('search-result')).toBeInTheDocument();
    });
  });

  describe('offerToDispatch logic', () => {
    it('sends offer with strikethrough if usePriceStrikethrough is true', async () => {
      mocked(usePriceStrikethrough).mockReturnValue({ showFullTraffic: true, showVariationB: false });
      renderComponent();

      await userEvent.click(screen.getByTestId('search-result'));

      const expectedOffer = {
        charges: {
          total: {
            amount: 100,
            currency: 'AUD',
          },
          strikethrough: {
            amount: 120,
            currency: 'AUD',
          },
        },
        type: 'offer',
        allocationsAvailable: 3,
        promotion: {
          name: 'Summer Sale',
        },
      };

      expect(mockFireSelectItemEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          offer: expectedOffer,
        }),
      );
    });

    it('sends offer without strikethrough if usePriceStrikethrough is false', async () => {
      mocked(usePriceStrikethrough).mockReturnValue({ isPriceStrikethrough: false });
      renderComponent();

      await userEvent.click(screen.getByTestId('search-result'));

      const expectedOffer = {
        charges: {
          total: {
            amount: 100,
            currency: 'AUD',
          },
        },
        type: 'offer',
        allocationsAvailable: 3,
        promotion: {
          name: 'Summer Sale',
        },
      };

      expect(mockFireSelectItemEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          offer: expectedOffer,
        }),
      );
      const callArgs = mockFireSelectItemEvent.mock.calls[0][0];
      expect(callArgs.offer.charges).not.toHaveProperty('strikethrough');
    });

    it('sends offer as is if no strikethrough property exists', async () => {
      delete result.offer.charges.strikethrough;
      mocked(usePriceStrikethrough).mockReturnValue({ isPriceStrikethrough: false });
      renderComponent();

      await userEvent.click(screen.getByTestId('search-result'));

      const expectedOffer = {
        charges: {
          total: {
            amount: 100,
            currency: 'AUD',
          },
        },
        type: 'offer',
        allocationsAvailable: 3,
        promotion: {
          name: 'Summer Sale',
        },
      };

      expect(mockFireSelectItemEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          offer: expectedOffer,
        }),
      );

      const callArgs = mockFireSelectItemEvent.mock.calls[0][0];
      expect(callArgs.offer.charges).toEqual(expectedOffer.charges);
    });
  });
});
