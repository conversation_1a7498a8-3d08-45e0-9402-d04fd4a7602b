import React from 'react';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { useTheme, ThemeProviderProps } from 'emotion-theming';
import { useSelector } from 'react-redux';
import { Flex, Box, Heading, Icon, ImageGallery, ImageGalleryProps, Text } from '@qga/roo-ui/components';
import { find, get, isArray } from 'lodash';
import { useDataLayer } from 'hooks/useDataLayer';
import Currency from 'components/Currency';
import Truncate from 'components/Truncate';
import PointsPerDollar from 'components/PointsPerDollar';
import PromotionalSashNew from '../../PromotionalSashNew';
import { mediaQuery } from 'lib/styledSystem';
import CampaignPriceMessage from 'components/CampaignPriceMessage';
import { getQueryCheckIn, getQueryCheckOut } from 'store/router/routerSelectors';
import CancellationRefundSummary from 'components/CancellationRefundSummary/CancellationRefundSummary';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { getNumberOfNights } from 'lib/date/numberOfNights';
import SearchResultLink from './SearchResultLink';
import { POINTS_PER_DOLLAR_DEFAULT, POINTS_EARN_ENABLED } from 'config';
import { getCampaignDefaultSash } from 'store/campaign/campaignSelectors';
import { Offer, Property, RoomType } from '../../../types/property';
import ImageFallback from 'components/Image/ImageFallback';
import Inclusions from '../Inclusions';
import RoomsAvailabilityMessage from 'components/RoomsAvailabilityMessage';
import PriceStrikethrough from 'components/PriceStrikethrough';
import PointsEarnSummary from 'components/PointsEarnSummary';
import { useShowFullPointsToggle } from './hooks/useShowFullPointsToggle';
import { useShowRedSashToggle } from 'hooks/useShowRedSashToggle';
import PromotionalSashRedVariant from 'components/PromotionalSashRedVariant';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough';
import PropertyRatings from 'components/PropertyRatings';
import PriceBeforeDiscount from 'components/PriceBeforeDiscount';
import usePointsRedemptionStrikethrough from 'hooks/optimizely/usePointsRedemptionStrikethrough';
import OfferDepositPayMessage from '../../PropertyPage/PropertyAvailability/RoomTypes/RoomTypeList/Offer/components/OfferDepositPayMessage';
import PointsRedemptionSash from 'components/PointsRedemptionSash';
import { FormatNumber } from 'components/formatters';

type Props = {
  result: {
    property: Property;
    offer: Offer;
    roomType: RoomType;
  };
  cardIndex: number;
};

interface ThemeProps extends ThemeProviderProps<{ imageSizes: { searchThumbnail: { width: string } } }> {
  imageSizes: { searchThumbnail: { width: string } };
}

interface GalleryProps extends ImageGalleryProps {
  showFullscreenButton?: boolean;
  className?: string;
}

const SearchImageGallery = styled(ImageGallery)<GalleryProps>``;

const PromotionalSashContainer = styled(Box)`
  margin-top: -1px;
  position: absolute;
  margin-right: 680px;
  z-index: ${themeGet('zIndices.dropdownContent')};
  ${mediaQuery.minWidth.sm} {
    margin-top: 0;
  }
`;

const ResultDetailsContainer = styled(Flex)`
  height: 100%;
  padding: ${themeGet('space.4')} ${themeGet('space.1')} ${themeGet('space.4')} ${themeGet('space.4')};
  flex-direction: column;
  flex-basis: auto;
  justify-content: flex-start;
`;

const StyledSymbol = styled(Box)`
  font-size: ${themeGet('fontSizes.sm')};
  margin-left: ${themeGet('space.1')};
  margin-right: ${themeGet('space.1')};
  font-weight: ${themeGet('fontWeights.bold')};
`;

const getTripAdvisorRating = (customerRatings) => {
  if (!isArray(customerRatings)) return null;
  return find(customerRatings, (rating) => rating?.source === 'trip_advisor');
};

const getPromotionalSash = (offer, globalCampaignDefaultSash) => {
  const isClassic = offer?.type === 'classic';
  const useGlobalCampaignDefaultSash = globalCampaignDefaultSash && !isClassic;
  const promotionSash = useGlobalCampaignDefaultSash ? globalCampaignDefaultSash : get(offer, 'promotion.name');

  if (promotionSash === 'Featured' && offer.luxOffer) {
    return `Featured \u2022 Luxury Offer`;
  } else {
    return promotionSash;
  }
};

const SearchResultDetail = React.memo(({ result, cardIndex }: Props) => {
  const theme = useTheme<ThemeProps>();
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobile = isLessThanBreakpoint(0);
  const checkIn = useSelector(getQueryCheckIn);
  const checkOut = useSelector(getQueryCheckOut);
  const { emitInteractionEvent } = useDataLayer();
  const globalCampaignDefaultSash = useSelector(getCampaignDefaultSash);
  const { isReady: isFullPointsReady, showFullPoints } = useShowFullPointsToggle();

  const { property, offer } = result;
  const { rating, ratingType } = property;
  const { inclusions, charges, cancellationPolicy, type } = offer;

  const handleGalleryImageChanged = () => {
    emitInteractionEvent({ type: 'ListSearch Property Gallery', value: `${property?.name} Gallery Scrolled}` });
  };

  const tripAdvisorRatingItem = getTripAdvisorRating(property?.customerRatings);
  const hasInclusions = inclusions ? inclusions?.length > 0 : false;
  // this will be updated to map fields from images once search response contains the field images
  const propertyImageSrcSet = property?.mainImage
    ? `${get(property, 'mainImage.urlLarge')} 2x, ${get(property, 'mainImage.urlOriginal')} 1x`
    : undefined;
  const propertyImageSrc = property?.mainImage ? `${get(property, 'mainImage.urlOriginal')}` : '';
  // this will use the search images field once it is available
  const images = [
    {
      src: propertyImageSrc,
      srcSet: propertyImageSrcSet,
      alt: property?.mainImage?.caption,
      thumbnail: property?.mainImage?.urlSmall,
      index: 0,
      total: 1,
    },
  ];

  const { total, totalBeforeDiscount, totalDiscount } = charges;
  const hasDiscount = totalDiscount?.amount > 0;
  const promotionSash = getPromotionalSash(offer, globalCampaignDefaultSash);
  const numberOfNights = getNumberOfNights({ checkIn, checkOut });
  const currency = total.currency;
  const isCurrencyCash = currency !== 'PTS';
  const ariaCurrency = currency === 'PTS' ? 'points' : 'dollars';
  const pointsPerDollar = offer?.pointsEarned?.maxQffEarnPpd ? offer?.pointsEarned?.maxQffEarnPpd : POINTS_PER_DOLLAR_DEFAULT;
  const isClassic = offer?.type === 'classic';
  const isLuxuryOffer = !!offer?.luxOffer;
  const isDepositPay = offer?.depositPay?.depositPayable;
  const { isReady: isRedSashReady, showRedSash } = useShowRedSashToggle({ promotionName: promotionSash });

  const { isPointsRedemptionStrikethrough } = usePointsRedemptionStrikethrough();
  const showPointsRedemptionStrikethrough =
    !isCurrencyCash && !isLuxuryOffer && hasDiscount && !isClassic && isPointsRedemptionStrikethrough;
  const pointsValue = FormatNumber({ number: charges.totalDiscount?.amount ?? 0, decimalPlaces: 0 });

  const { showMessage, max_rooms_cutoff } = useAvailableRoomsMessage();

  const { showFullTraffic, showVariationB } = usePriceStrikethrough();
  const isPriceStrikethrough = showFullTraffic || showVariationB;
  const allocationsAvailable = get(offer, 'allocationsAvailable', 0);
  const availableRoomsMaxCutoff = max_rooms_cutoff ?? 5;
  const showAvailableRoomsMessage = showMessage && allocationsAvailable > 0 && allocationsAvailable <= availableRoomsMaxCutoff;

  const desktopImageHeight = showAvailableRoomsMessage ? '280px' : '255px';
  const imageHeight = isMobile ? '144px' : desktopImageHeight;

  const strikethroughPrice = offer?.charges?.strikethrough?.price;
  const isPriceStrikeThroughAvailable = !!strikethroughPrice?.amount;
  const showPriceStrikethrough = isPriceStrikeThroughAvailable && isPriceStrikethrough && !isClassic;
  return (
    <>
      {promotionSash && (
        <PromotionalSashContainer>
          {isRedSashReady && showRedSash ? (
            <PromotionalSashRedVariant promotionName={promotionSash} data-testid="promotional-sash-red" />
          ) : (
            <PromotionalSashNew promotionName={promotionSash} data-testid="promotional-sash" />
          )}
        </PromotionalSashContainer>
      )}

      <Flex flex="1 1 auto" justifyContent="space-between" flexDirection={['column', 'row']}>
        <Box
          flexBasis={theme.imageSizes.searchThumbnail.width}
          borderRadius={isMobile ? '4px 4px 0px 0px' : '4px 0px 0px 4px'}
          overflow="hidden"
        >
          {propertyImageSrc.length > 0 && (
            <SearchImageGallery
              images={images}
              onImageChanged={handleGalleryImageChanged}
              height={imageHeight}
              showFullscreenButton={false}
            />
          )}
          {!propertyImageSrc && <ImageFallback alt="Featured hotel placeholder" height={imageHeight} width="100%" />}
        </Box>
        <Flex flex="1 1 0px" flexDirection="column" justifyContent="space-between">
          <ResultDetailsContainer>
            <Flex flexDirection="column" pb={2}>
              <SearchResultLink
                propertyId={property.id}
                cardIndex={cardIndex}
                ariaLabel={`${property.name} from ${parseInt(total.amount)} ${ariaCurrency}`}
              >
                <Truncate lines={2}>
                  <Heading.h2
                    color="greys.charcoal"
                    pt={isMobile ? 1 : 0}
                    mb={0}
                    fontSize={['md', 'lg']}
                    data-testid="property-name"
                    lineHeight={['28px', 'normal']}
                  >
                    {property.name}
                  </Heading.h2>
                </Truncate>
              </SearchResultLink>

              <Heading.h3 display={['block']} color="greys.dusty" fontSize="sm" mb={1} data-testid="property-suburb">
                {property?.address?.suburb}
              </Heading.h3>

              <Flex mt={1} alignItems="center" flexDirection="row">
                <PropertyRatings
                  hotelRating={rating}
                  hotelRatingType={ratingType}
                  hotelRatingSize={16}
                  customerRating={tripAdvisorRatingItem}
                  displayTAReviews
                />
              </Flex>
            </Flex>

            {hasInclusions && isMobile && <Inclusions inclusions={inclusions} />}

            <Flex>
              {hasInclusions && !isMobile && <Inclusions inclusions={inclusions} />}
              <Flex pr={4} flex="1 1 auto" flexDirection="column" alignItems="flex-end" justifyContent="flex-end">
                <>
                  <Text
                    display="block"
                    color="greys.charcoal"
                    lineHeight="30px"
                    fontSize={'sm'}
                    data-testid="number-of-nights"
                    mb={isCurrencyCash ? '-11px' : '-10px'}
                  >
                    {numberOfNights} from{' '}
                  </Text>
                  <Flex alignItems="flex-start" mt={2} mr="-1px">
                    {isClassic && <Icon ml="-6px" name="ribbon" size={28} data-testid="classic-ribbon" color="greys.charcoal" />}
                    {showPointsRedemptionStrikethrough && (
                      <Box mt={1} mr={3}>
                        <PriceBeforeDiscount
                          total={totalBeforeDiscount}
                          discount={totalDiscount}
                          hideCurrency={isCurrencyCash}
                          fontSize={['xs', 'sm']}
                          lineHeight="0.6"
                          roundToCeiling
                          offerType={offer.type}
                          showMessage={false}
                        />
                      </Box>
                    )}
                    <Box>
                      {isCurrencyCash && !showPriceStrikethrough && <StyledSymbol data-testid="currency-cash-symbol">AUD</StyledSymbol>}
                      {showPriceStrikethrough && <PriceStrikethrough price={strikethroughPrice} asSup />}
                    </Box>
                    <Currency
                      testId="currency"
                      amount={total.amount}
                      currency={total.currency}
                      roundToCeiling
                      fontSize={['32px', 'xl']}
                      hideCurrency={true}
                      color="greys.charcoal"
                      fontWeight="bold"
                      alignCurrency="superscript"
                    />
                    {!isCurrencyCash && <StyledSymbol data-testid="points-plus-asterisk">PTS*</StyledSymbol>}
                  </Flex>
                </>
                {showPointsRedemptionStrikethrough ? (
                  <Box mt={1}>
                    <PointsRedemptionSash campaignMessage={`Save ${pointsValue} PTS`} backgroundColor="green" color="white" />
                  </Box>
                ) : (
                  <CampaignPriceMessage currency={currency} offerType={type} fallback />
                )}
                {POINTS_EARN_ENABLED && isCurrencyCash && (
                  <>
                    {isFullPointsReady && showFullPoints ? (
                      <PointsEarnSummary
                        {...offer?.pointsEarned?.qffPoints}
                        fontSize={['xs', 'sm']}
                        topMargin={1}
                        luxuryInclusions={true}
                      />
                    ) : (
                      <PointsPerDollar
                        pointsPerDollar={pointsPerDollar}
                        strikeThroughDefaultPoints={true}
                        fontSize={['xs', 'sm']}
                        display="block"
                        luxuryInclusions={true}
                      />
                    )}
                  </>
                )}
                <CancellationRefundSummary
                  cancellationPolicy={cancellationPolicy}
                  fontSize={['xs', 'sm']}
                  hideBeforeDate
                  hideWhenNonRefundable
                  display="block"
                  luxuryInclusions={true}
                />
                {showAvailableRoomsMessage && <RoomsAvailabilityMessage allocationsAvailable={allocationsAvailable} />}
                {isDepositPay && <OfferDepositPayMessage depositPay={offer.depositPay} />}
              </Flex>
            </Flex>
          </ResultDetailsContainer>
        </Flex>
      </Flex>
    </>
  );
});

SearchResultDetail.displayName = 'SearchResultDetail';

export default SearchResultDetail;
