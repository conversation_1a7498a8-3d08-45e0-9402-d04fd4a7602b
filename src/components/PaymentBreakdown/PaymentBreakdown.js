import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { Decimal } from 'decimal.js';
import { format as formatDate } from 'date-fns';
import { Flex, Box, Text, Icon } from '@qga/roo-ui/components';

import { DISPLAY_DATE_FORMAT } from 'config';
import { getInitialCashAmount } from 'store/checkout/checkoutSelectors';

import Currency from 'components/Currency';
import PriceStrikethrough from 'components/PriceStrikethrough';

import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough';

const priceStyle = { fontWeight: 'bold', fontSize: ['sm', 'base'], lineHeight: 'normal' };
const rowStyle = { justifyContent: 'space-between', alignItems: 'center', mt: 1 };

const PaymentBreakdown = ({
  headingText,
  pointsAmount,
  travelPassAmount,
  payableNowCashAmount,
  payableLaterCashAmount,
  payableLaterDueDate,
  payableAtProperty,
  priceStrikethrough,
}) => {
  const hasPointsAmount = pointsAmount.greaterThan(0);
  const hasTravelPassAmount = travelPassAmount.greaterThan(0);
  const hasPayableNowCashAmount = payableNowCashAmount.greaterThan(0);
  const hasPayableLaterAmountAndDueDate = payableLaterCashAmount.greaterThan(0) && !!payableLaterDueDate;
  const hasPayableAtProperty = new Decimal(payableAtProperty.amount).greaterThan(0);
  const hasMultiplePaymentTypes = [hasPointsAmount, hasTravelPassAmount, hasPayableNowCashAmount].filter((truthy) => truthy).length > 1;

  const { showFullTraffic, showVariationB } = usePriceStrikethrough();
  const isPriceStrikethrough = showFullTraffic || showVariationB;
  const initialCashAmount = useSelector(getInitialCashAmount);
  const isPointsPlusPay = initialCashAmount.toNumber() > 0;
  const isPriceStrikeAvailable = priceStrikethrough?.amount > 0;
  const showPriceStrikethrough = isPriceStrikeAvailable && isPriceStrikethrough && !isPointsPlusPay;

  const TotalToPayNowLabel = () => (
    <Text fontWeight="bold" fontSize={['sm', 'base']} data-testid="total-to-pay-now-label">
      {showPriceStrikethrough ? 'Total' : headingText}
    </Text>
  );

  return (
    <Box data-testid="payment-breakdown">
      {(hasMultiplePaymentTypes || hasTravelPassAmount) && <TotalToPayNowLabel />}
      {!hasPointsAmount && !hasTravelPassAmount && !hasPayableNowCashAmount && (
        <Flex {...rowStyle} data-testid="zero-payable-amount">
          <Box>
            <TotalToPayNowLabel />
          </Box>
          <Currency amount={payableNowCashAmount} currency="AUD" {...priceStyle} />
        </Flex>
      )}
      {hasTravelPassAmount && (
        <Flex {...rowStyle} data-testid="travel-pass-amount">
          <Box>
            <Text fontSize={['sm', 'base']}>Qantas Passes or Credit</Text>
          </Box>
          <Currency amount={travelPassAmount} currency="AUD" {...priceStyle} />
        </Flex>
      )}
      {hasPointsAmount && (
        <Flex {...rowStyle} data-testid="points-amount">
          <Box>{hasMultiplePaymentTypes ? <Text fontSize={['sm', 'base']}>Qantas Points</Text> : <TotalToPayNowLabel />}</Box>
          <Flex {...rowStyle}>
            {showPriceStrikethrough && <PriceStrikethrough withDecimal price={priceStrikethrough} mr={2} />}
            <Currency amount={pointsAmount} currency="PTS" {...priceStyle} />*
          </Flex>
        </Flex>
      )}
      {hasPayableNowCashAmount && (
        <Flex {...rowStyle} data-testid="cash-payable-now-amount">
          <Box>{hasMultiplePaymentTypes ? <Text fontSize={['sm', 'base']}>Cash</Text> : <TotalToPayNowLabel />}</Box>
          <Flex {...rowStyle}>
            {showPriceStrikethrough && <PriceStrikethrough withDecimal price={priceStrikethrough} mr={2} />}
            <Currency amount={payableNowCashAmount} currency="AUD" {...priceStyle} />
          </Flex>
        </Flex>
      )}
      {hasPayableLaterAmountAndDueDate && (
        <Fragment>
          <Flex {...rowStyle} mt={4} mb={1} data-testid="cash-payable-later-amount">
            <Flex alignItems="center">
              <Icon name="depositPay" mr={2} size={22} />
              <Text fontSize={['sm', 'base']}>Pay later</Text>
            </Flex>
            <Currency amount={payableLaterCashAmount} currency="AUD" {...priceStyle} />
          </Flex>
          <Text color="greys.steel" fontSize="xs" lineHeight="tight" data-testid="payable-later-due-date-message">
            Final payment will be automatically charged to your credit card on {formatDate(payableLaterDueDate, DISPLAY_DATE_FORMAT)}
          </Text>
        </Fragment>
      )}
      {hasPayableAtProperty && (
        <Fragment>
          <Flex {...rowStyle} mb={1} data-testid="payable-at-property-amount" {...priceStyle}>
            <Box>
              <Text fontSize={['sm', 'base']}>Pay later at property</Text>
            </Box>
            <Currency amount={payableAtProperty.amount} currency={payableAtProperty.currency} {...priceStyle} />
          </Flex>
          <Text color="greys.steel" fontSize="xs" lineHeight="tight" data-testid="price-label-subtext">
            Payable in local currency at check-in
          </Text>
        </Fragment>
      )}
    </Box>
  );
};

PaymentBreakdown.propTypes = {
  pointsAmount: PropTypes.instanceOf(Decimal).isRequired,
  travelPassAmount: PropTypes.instanceOf(Decimal).isRequired,
  payableNowCashAmount: PropTypes.instanceOf(Decimal).isRequired,
  payableLaterCashAmount: PropTypes.instanceOf(Decimal).isRequired,
  payableLaterDueDate: PropTypes.instanceOf(Date),
  payableAtProperty: PropTypes.shape({
    amount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    currency: PropTypes.string,
  }).isRequired,
  headingText: PropTypes.string,
  priceStrikethrough: PropTypes.shape({
    amount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    currency: PropTypes.string,
  }),
};

PaymentBreakdown.defaultProps = {
  payableLaterDueDate: null,
  headingText: 'Total due now',
  priceStrikethrough: null,
};

export default PaymentBreakdown;
