import React from 'react';
import { Decimal } from 'decimal.js';
import { screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import PaymentBreakdown from './PaymentBreakdown';
import { getInitialCashAmount } from 'store/checkout/checkoutSelectors';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough';
import { renderWithProviders } from 'test-utils/reactUtils';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough');

const pointsAmount = new Decimal(0);
const travelPassAmount = new Decimal(0);
const payableNowCashAmount = new Decimal(0);
const payableLaterCashAmount = new Decimal(0);
const initialCashAmount = new Decimal(0);
const payableLaterDueDate = new Date(2020, 9, 9);
const payableAtProperty = { amount: '0', currency: 'AUD' };

const defaultProps = {
  pointsAmount,
  travelPassAmount,
  payableNowCashAmount,
  payableLaterCashAmount,
  payableLaterDueDate,
  payableAtProperty,
  priceStrikethrough: null,
};

const renderComponent = (props) => {
  return renderWithProviders(<PaymentBreakdown {...defaultProps} {...props} />);
};

describe('PaymentBreakdown', () => {
  beforeEach(() => {
    getInitialCashAmount.mockReturnValue(initialCashAmount);
    usePriceStrikethrough.mockReturnValue({
      isReady: true,
      showFullTraffic: false,
      showVariationB: false,
    });
  });

  describe('with only payableNowCashAmount', () => {
    it('renders a single cash amount with appropriate label', () => {
      renderComponent({ payableNowCashAmount: new Decimal(100) });
      expect(screen.getByTestId('cash-payable-now-amount')).toHaveTextContent('Total due now$100.00AUD');
      expect(screen.queryByTestId('points-amount')).not.toBeInTheDocument();
      expect(screen.queryByTestId('travel-pass-amount')).not.toBeInTheDocument();
      expect(screen.queryByTestId('cash-payable-later-amount')).not.toBeInTheDocument();
      expect(screen.queryByTestId('payable-at-property-amount')).not.toBeInTheDocument();
    });
  });

  describe('with only pointsAmount', () => {
    it('renders a single points amount with appropriate label', () => {
      renderComponent({ pointsAmount: new Decimal(100) });
      expect(screen.getByTestId('points-amount')).toHaveTextContent('Total due now100PTS*');
      expect(screen.queryByTestId('travel-pass-amount')).not.toBeInTheDocument();
      expect(screen.queryByTestId('cash-payable-now-amount')).not.toBeInTheDocument();
      expect(screen.queryByTestId('cash-payable-later-amount')).not.toBeInTheDocument();
      expect(screen.queryByTestId('payable-at-property-amount')).not.toBeInTheDocument();
    });
  });

  describe('with only travelPassAmount', () => {
    it('renders a single travel pass amount with appropriate label', () => {
      renderComponent({ travelPassAmount: new Decimal(100) });
      expect(screen.getByText('Total due now')).toBeInTheDocument();
      expect(screen.getByTestId('travel-pass-amount')).toHaveTextContent('Qantas Passes or Credit$100.00AUD');
      expect(screen.queryByTestId('cash-payable-now-amount')).not.toBeInTheDocument();
      expect(screen.queryByTestId('cash-payable-later-amount')).not.toBeInTheDocument();
      expect(screen.queryByTestId('payable-at-property-amount')).not.toBeInTheDocument();
    });
  });

  describe('when pointsAmount, travelPassAmount and payableNowCashAmount are zero (due to full voucher payment)', () => {
    it('renders total to pay now of zero dollars', () => {
      renderComponent();

      expect(screen.getByTestId('zero-payable-amount')).toHaveTextContent('Total due now$0.00AUD');
      expect(screen.queryByTestId('travel-pass-amount')).not.toBeInTheDocument();
      expect(screen.queryByTestId('points-amount')).not.toBeInTheDocument();
      expect(screen.queryByTestId('cash-payable-now-amount')).not.toBeInTheDocument();
      expect(screen.queryByTestId('cash-payable-later-amount')).not.toBeInTheDocument();
      expect(screen.queryByTestId('payable-at-property-amount')).not.toBeInTheDocument();
    });
  });

  describe('with a payableNowCashAmount, payableLaterCashAmount, pointsAmount and payableAtProperty', () => {
    it('renders all relevant payment breakdown items', () => {
      renderComponent({
        payableNowCashAmount: new Decimal(100),
        payableLaterCashAmount: new Decimal(200),
        pointsAmount: new Decimal(100),
        travelPassAmount: new Decimal(100),
        payableAtProperty: { amount: '10', currency: 'AUD' },
      });

      expect(screen.getByTestId('points-amount')).toHaveTextContent('Qantas Points100PTS*');
      expect(screen.getByTestId('travel-pass-amount')).toHaveTextContent('Qantas Passes or Credit$100.00AUD');
      expect(screen.getByTestId('cash-payable-now-amount')).toHaveTextContent('Cash$100.00AUD');
      expect(screen.getByTestId('cash-payable-later-amount')).toHaveTextContent('Pay later$200.00AUD');
      expect(screen.getByTestId('payable-at-property-amount')).toHaveTextContent('Pay later at property$10.00AUD');
    });
  });

  describe('with a payableLaterCashAmount and no due date', () => {
    it('does not render cash-payable-later-amount or due date message', () => {
      renderComponent({
        payableLaterCashAmount: new Decimal(200),
        payableLaterDueDate: null,
      });
      expect(screen.queryByTestId('cash-payable-later-amount')).not.toBeInTheDocument();
      expect(screen.queryByTestId('payable-later-due-date-message')).not.toBeInTheDocument();
    });
  });

  it('does NOT render the PointsAndCash component', () => {
    renderComponent();
    expect(screen.queryByText('PointsAndCash')).not.toBeInTheDocument();
  });

  describe('qantas-hotels-price-strikethrough feature', () => {
    describe('when the flag variant is not showFullTraffic or showVariationB', () => {
      it('does not render the price strikethrough', () => {
        renderComponent({ priceStrikethrough: { amount: 200, currency: 'AUD' } });
        expect(screen.queryByText(/Original price was/)).not.toBeInTheDocument();
      });
    });

    describe('when the flag variant is showFullTraffic or showVariationB', () => {
      beforeEach(() => {
        usePriceStrikethrough.mockReturnValue({
          isReady: true,
          showFullTraffic: false,
          showVariationB: true,
        });
      });

      describe('and the cash price strikethrough is available', () => {
        it('renders the cash total and discounted amount with strikethrough', () => {
          renderComponent({ payableNowCashAmount: new Decimal(100), priceStrikethrough: { amount: 200, currency: 'AUD' } });
          expect(screen.getByTestId('cash-payable-now-amount')).toHaveTextContent('Total$200.00 Original price was $200.00 $100.00AUD');
        });
      });

      describe('and the points price strikethrough is available', () => {
        it('renders the points full and discounted amount with strikethrough', () => {
          renderComponent({ pointsAmount: new Decimal(10000), priceStrikethrough: { amount: 200000, currency: 'PTS' } });
          expect(screen.getByTestId('points-amount')).toHaveTextContent('Total200,000 PTS Original price was 200,000 PTS 10,000PTS*');
        });
      });

      describe('the points price strikethrough is available but is points plus pay', () => {
        beforeEach(() => {
          getInitialCashAmount.mockReturnValue(new Decimal(150));
        });

        it('does not render the price strikethrough', () => {
          renderComponent({ priceStrikethrough: { amount: 200, currency: 'AUD' } });
          expect(screen.queryByText(/Original price was/)).not.toBeInTheDocument();
        });
      });
    });
  });
});
