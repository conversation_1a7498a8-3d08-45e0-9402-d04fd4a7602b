import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import get from 'lodash/get';
import { Flex, Box, Text } from '@qga/roo-ui/components';

import {
  getPointsAmount,
  getVoucherAmount,
  getPayableNowCashAmount,
  getPayableLaterCashAmount,
  getTravelPassAmount,
  getInitialCashAmount,
  getPayWith,
} from 'store/checkout/checkoutSelectors';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';

import PaymentBreakdown from 'components/PaymentBreakdown';
import PriceBreakdownModal from 'components/CheckoutPage/PriceBreakdownModal';
import Currency from 'components/Currency';
import PriceStrikethrough from 'components/PriceStrikethrough';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough';
import { getQuotePriceStrikethrough } from 'components/PriceStrikethrough/helper.ts';

const PriceDetails = ({ property, offer, checkIn, checkOut, ...rest }) => {
  const { charges } = offer;
  const payableAtProperty = get(charges, 'payableAtProperty.total');
  const payableAtBooking = get(charges, 'payableAtBooking.total', null);
  const payWith = useSelector(getPayWith);
  const priceStrikethrough = getQuotePriceStrikethrough(charges, payWith);

  const voucherAmount = useSelector(getVoucherAmount);
  const pointsAmount = useSelector(getPointsAmount);
  const travelPassAmount = useSelector(getTravelPassAmount);
  const payableNowCashAmount = useSelector(getPayableNowCashAmount);
  const payableLaterCashAmount = useSelector(getPayableLaterCashAmount);
  const payableLaterDueDate = useSelector(getPayableLaterDueDate);

  const { showFullTraffic, showVariationB } = usePriceStrikethrough();
  const isPriceStrikethrough = showFullTraffic || showVariationB;

  const isPriceStrikeAvailable = priceStrikethrough?.amount > 0;
  const initialCashAmount = useSelector(getInitialCashAmount);
  const isPointsPlusPay = initialCashAmount.toNumber() > 0;
  const totalPoints = payableAtBooking?.points;

  const showPriceStrikethrough = isPriceStrikeAvailable && isPriceStrikethrough && isPointsPlusPay;

  return (
    <Box {...rest}>
      {showPriceStrikethrough && (
        <>
          <Flex justifyContent={'space-between'} alignItems="center" mb={2}>
            <Text fontWeight="bold" fontSize={['sm', 'base']}>
              Total
            </Text>
            <Flex justifyContent="space-between" alignItems="center">
              <PriceStrikethrough price={priceStrikethrough} withDecimal />
              <Currency amount={totalPoints} currency="PTS" fontWeight={'bold'} fontSize={['sm', 'base']} />
            </Flex>
          </Flex>
          <PriceBreakdownModal
            property={property}
            offer={offer}
            checkIn={checkIn}
            checkOut={checkOut}
            data-testid="price-breakdown-modal-ppp"
          />
          <Box borderBottom={1} py={0} my={2} color="brand.secondary" />
        </>
      )}
      {!voucherAmount.isZero() && (
        <Flex pb={[2, 3]} mb={[3, 6]} borderBottom={1} borderColor="greys.alto" justifyContent="space-between">
          <Box flex={['0 0 170px', '0 0 200px']} fontSize={['sm', 'base']}>
            Voucher
          </Box>
          <Currency amount={voucherAmount.negated()} currency="AUD" fontSize={['sm', 'base']} data-testid="voucher-amount" />
        </Flex>
      )}
      <PaymentBreakdown
        payableNowCashAmount={payableNowCashAmount}
        payableLaterCashAmount={payableLaterCashAmount}
        payableLaterDueDate={payableLaterDueDate}
        pointsAmount={pointsAmount}
        travelPassAmount={travelPassAmount}
        payableAtProperty={payableAtProperty}
        priceStrikethrough={isPointsPlusPay ? null : priceStrikethrough}
      />
      {!showPriceStrikethrough && (
        <PriceBreakdownModal property={property} offer={offer} checkIn={checkIn} checkOut={checkOut} data-testid="price-breakdown-modal" />
      )}
    </Box>
  );
};

PriceDetails.propTypes = {
  property: PropTypes.shape({
    name: PropTypes.string,
  }),
  offer: PropTypes.shape({
    charges: PropTypes.object,
    type: PropTypes.string,
  }),
  checkIn: PropTypes.instanceOf(Date).isRequired,
  checkOut: PropTypes.instanceOf(Date).isRequired,
};

PriceDetails.defaultProps = {
  property: { name: undefined },
  offer: { charges: {}, type: undefined },
};

export default PriceDetails;
