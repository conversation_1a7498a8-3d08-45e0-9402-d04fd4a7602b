import React from 'react';
import { render, screen } from '@testing-library/react';
import { Decimal } from 'decimal.js';
import PriceDetails from './PriceDetails';
import {
  getPointsAmount,
  getTravelPassAmount,
  getPayableNowCashAmount,
  getPayableLaterCashAmount,
  getInitialCashAmount,
  getVoucherAmount,
} from 'store/checkout/checkoutSelectors';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';
import { Provider } from 'react-redux';
import testStore from 'redux-mock-store';
import ThemeProvider from 'components/ThemeProvider';
import theme from 'lib/theme';
import { BrowserRouter as Router } from 'react-router-dom';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough');

const pointsAmount = new Decimal(1000);
const travelPassAmount = new Decimal(50);
const payableLaterDueDate = new Date(2020, 10, 10);
const payableNowCashAmount = new Decimal(100);
const payableLaterCashAmount = new Decimal(100);
const initialCashAmount = new Decimal(0);
const voucherAmount = new Decimal(50);

const payableAtPropertyTotal = {
  amount: '10',
  currency: 'AUD',
};
const property = {
  name: 'Test hotel',
};

const payableAtBookingBaseRate = {
  amount: '320.00',
  currency: 'AUD',
};

const taxDisplayable = {
  amount: '12.00',
  currency: 'AUD',
};

const extraOccupantCharge = {
  amount: '50.00',
  currency: 'AUD',
};

const taxRecoveryBreakdown = [
  {
    description: 'TaxAndServiceFee',
    charge: {
      amount: '12.00',
      currency: 'AUD',
    },
  },
  {
    description: 'PropertyFee',
    charge: {
      amount: '20.00',
      currency: 'AUD',
    },
  },
];

const strikethrough = {
  total: {
    pointsStrikethrough: 12000,
  },
};

const offer = {
  type: 'standard',
  charges: {
    payableAtProperty: { total: payableAtPropertyTotal },
    payableAtBooking: { baseRate: payableAtBookingBaseRate, taxDisplayable, taxRecoveryBreakdown, extraOccupantCharge, ...strikethrough },
  },
};

const checkIn = new Date(2020, 9, 1);
const checkOut = new Date(2020, 9, 2);

const defaultProps = {
  property,
  offer,
  checkIn,
  checkOut,
};
const mockStore = testStore([]);
const initialState = { browser: { name: 'Chrome' } };

const renderWithProviders = () => {
  const store = mockStore(initialState);
  return render(
    <Provider store={store}>
      <Router>
        <ThemeProvider theme={theme}>
          <PriceDetails {...defaultProps} />
        </ThemeProvider>
      </Router>
    </Provider>,
  );
};

beforeEach(() => {
  getPointsAmount.mockReturnValue(pointsAmount);
  getTravelPassAmount.mockReturnValue(travelPassAmount);
  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getPayableLaterCashAmount.mockReturnValue(payableLaterCashAmount);
  getPayableLaterDueDate.mockReturnValue(payableLaterDueDate);
  getInitialCashAmount.mockReturnValue(initialCashAmount);
  getVoucherAmount.mockReturnValue(voucherAmount);
  usePriceStrikethrough.mockReturnValue({
    isReady: true,
    showFullTraffic: false,
    showVariationB: false,
  });
});

it('renders the PaymentBreakdown', () => {
  renderWithProviders();

  expect(screen.getByTestId('payment-breakdown')).toBeInTheDocument();
});

it('renders the PriceBreakdownModal', () => {
  renderWithProviders();

  expect(screen.getByText('Price breakdown', { selector: 'button' })).toBeInTheDocument();
});
