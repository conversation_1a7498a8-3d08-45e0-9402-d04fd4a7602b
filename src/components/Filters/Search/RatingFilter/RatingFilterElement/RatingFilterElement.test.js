import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { useDispatch } from 'react-redux';
import { filterUpdate } from 'store/search/searchActions';
import RatingFilterElement from './RatingFilterElement';

jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
}));

const mockDispatch = jest.fn();
const mockOnChange = jest.fn();

jest.mock('@qga/roo-ui/components', () => ({
  Radio: ({ name, value, onChange, checked, 'aria-labelledby': ariaLabelledBy }) => (
    <input
      type="radio"
      name={name}
      value={value}
      onChange={onChange}
      checked={checked}
      id={`${name}-${value}`}
      aria-labelledby={ariaLabelledBy}
    />
  ),
  StarRating: ({ ratingType, rating, size }) => <div data-testid="mock-star-rating">{`StarRating: ${ratingType} ${rating} ${size}`}</div>,
  Box: ({ 'data-testid': testId, children }) => <div data-testid={testId}>{children}</div>,
  Label: ({ children, id }) => <label id={id}>{children}</label>,
  Text: ({ children }) => <span>{children}</span>,
}));

jest.mock('components/PropertyRatings/TripAdvisorRating/RatingCircles', () => ({
  __esModule: true,
  default: ({ rating, size }) => <div data-testid="mock-tripadvisor-rating">{`TripAdvisorRating: ${rating} ${size}`}</div>,
}));

beforeEach(() => {
  useDispatch.mockReturnValue(mockDispatch);
  mockOnChange.mockClear();
  mockDispatch.mockClear();
});

describe('<RatingFilterElement />', () => {
  const defaultProps = {
    ratingType: 'minStarRating',
    text: 'My star rating',
    currentRating: '5',
    rating: '5',
    showRatingStars: true,
    onChange: mockOnChange,
  };

  it('renders the label with the correct text (implicitly via radio)', () => {
    render(<RatingFilterElement {...defaultProps} />);
    expect(screen.getByRole('radio', { name: /my star rating/i })).toBeInTheDocument();
  });

  it('renders the radio button with expected name and value', () => {
    render(<RatingFilterElement {...defaultProps} />);
    const radio = screen.getByRole('radio', { name: /my star rating/i });
    expect(radio).toHaveAttribute('name', 'minStarRating');
    expect(radio).toHaveAttribute('value', '5');
  });

  it('checks the radio button when currentRating is equal to rating', () => {
    render(<RatingFilterElement {...defaultProps} currentRating="5" rating="5" />);
    const radio = screen.getByRole('radio', { name: /my star rating/i });
    expect(radio).toBeChecked();
  });

  it('does not check the radio button when currentRating is not equal to rating', () => {
    render(<RatingFilterElement {...defaultProps} currentRating="4" rating="5" />);
    const radio = screen.getByRole('radio', { name: /my star rating/i });
    expect(radio).not.toBeChecked();
  });

  it('checks the radio button when both rating and currentRating are undefined', () => {
    render(<RatingFilterElement {...defaultProps} rating={undefined} currentRating={undefined} />);
    const radio = screen.getByRole('radio', { name: /my star rating/i });
    expect(radio).toBeChecked();
  });

  it('does not display StarRating when showRatingStars is false', () => {
    render(<RatingFilterElement {...defaultProps} showRatingStars={false} />);
    expect(screen.queryByTestId('star-rating')).not.toBeInTheDocument();
    expect(screen.queryByTestId('mock-star-rating')).not.toBeInTheDocument();
  });

  it('calls onChange with the correct value when a star rating is selected', async () => {
    render(<RatingFilterElement {...defaultProps} rating="3" />);
    const radio = screen.getByRole('radio', { name: /my star rating/i });
    await userEvent.click(radio);
    expect(mockOnChange).toHaveBeenCalledWith({ minStarRating: '3' });
  });

  it('renders StarRating for minStarRating type', () => {
    render(<RatingFilterElement {...defaultProps} ratingType="minStarRating" />);
    expect(screen.getByTestId('mock-star-rating')).toBeInTheDocument();
    expect(screen.queryByTestId('mock-tripadvisor-rating')).not.toBeInTheDocument();
  });

  it('renders TripAdvisorRating for minTripadvisorRating type', () => {
    render(<RatingFilterElement {...defaultProps} ratingType="minTripadvisorRating" />);
    expect(screen.getByTestId('mock-tripadvisor-rating')).toBeInTheDocument();
    expect(screen.queryByTestId('mock-star-rating')).not.toBeInTheDocument();
  });

  it('dispatches filterUpdate for minStarRating when a value is selected', async () => {
    render(<RatingFilterElement {...defaultProps} rating="3" ratingType="minStarRating" />);
    const radio = screen.getByRole('radio', { name: /my star rating/i });
    await userEvent.click(radio);

    expect(mockDispatch).toHaveBeenCalledWith(
      filterUpdate({
        groupName: 'Hotel Rating',
        itemName: 'hotel rating',
        itemText: 'My star rating',
        itemType: 'radio',
        itemValue: 'My star rating',
      }),
    );
  });

  it('dispatches filterUpdate for minTripadvisorRating when a value is selected', async () => {
    render(<RatingFilterElement {...defaultProps} rating="3" ratingType="minTripadvisorRating" />);
    const radio = screen.getByRole('radio', { name: /my star rating/i });
    await userEvent.click(radio);

    expect(mockDispatch).toHaveBeenCalledWith(
      filterUpdate({
        groupName: 'Tripadvisor Rating',
        itemName: 'tripadvisor rating',
        itemText: 'My star rating',
        itemType: 'radio',
        itemValue: 'My star rating',
      }),
    );
  });

  it('applies the correct aria-labelledby to the radio input', () => {
    render(<RatingFilterElement {...defaultProps} ratingType="minStarRating" text="Hotel Stars" />);
    const radioInput = screen.getByRole('radio', { name: /hotel stars/i });
    expect(radioInput).toHaveAttribute('aria-labelledby', 'minStarRating-hotel-stars');
  });

  it('displays the text prop within a Text component', () => {
    render(<RatingFilterElement {...defaultProps} text="Any Rating" />);
    expect(screen.getByText('Any Rating')).toBeInTheDocument();
  });

  it('renders without rating and currentRating props, defaulting to checked', () => {
    render(<RatingFilterElement {...defaultProps} rating={undefined} currentRating={undefined} />);
    const radio = screen.getByRole('radio', { name: /my star rating/i });
    expect(radio).toBeChecked();
  });

  it('handles `rating` prop as undefined and `currentRating` defined correctly (not checked)', () => {
    render(<RatingFilterElement {...defaultProps} rating={undefined} currentRating="3" />);
    const radio = screen.getByRole('radio', { name: /my star rating/i });
    expect(radio).not.toBeChecked();
  });

  it('handles `rating` prop defined and `currentRating` undefined correctly (not checked)', () => {
    render(<RatingFilterElement {...defaultProps} rating="5" currentRating={undefined} />);
    const radio = screen.getByRole('radio', { name: /my star rating/i });
    expect(radio).not.toBeChecked();
  });

  it('ensures accessibility by checking label association with radio button', () => {
    render(<RatingFilterElement {...defaultProps} ratingType="minStarRating" text="Accessibility Test" />);
    const radio = screen.getByRole('radio', { name: /accessibility test/i });
    expect(radio).toBeInTheDocument();
    expect(radio).toHaveAttribute('id', 'minStarRating-5');
    expect(radio).toHaveAttribute('aria-labelledby', 'minStarRating-accessibility-test');
  });

  it('marks the radio button as checked after a click if it was unchecked', async () => {
    render(<RatingFilterElement {...defaultProps} currentRating="4" rating="5" />);
    const radio = screen.getByRole('radio', { name: /my star rating/i });
    expect(radio).not.toBeChecked();
    await userEvent.click(radio);
    expect(mockOnChange).toHaveBeenCalledWith({ minStarRating: '5' });
  });

  it('does not uncheck the radio button when clicking an already checked radio button (typical radio behavior)', async () => {
    render(<RatingFilterElement {...defaultProps} currentRating="5" rating="5" />);
    const radio = screen.getByRole('radio', { name: /my star rating/i });
    expect(radio).toBeChecked();
    await userEvent.click(radio);
    expect(mockOnChange).not.toHaveBeenCalled();
    expect(radio).toBeChecked();
  });
});
