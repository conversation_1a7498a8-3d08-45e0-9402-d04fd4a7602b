import React from 'react';
import { Flex, Text, Button, Icon, Link } from '@qga/roo-ui/components';
import styled from '@emotion/styled';
import { useDataLayer } from 'hooks/useDataLayer';
import { format } from 'lib/date';
import { useSelector } from 'react-redux';
import { themeGet } from 'styled-system';
import {
  getExclusiveOffer,
  getIsLoading as getIsLoadingExclusiveOffers,
  getLeadInOffer,
} from 'store/exclusiveOffer/exclusiveOfferSelectors';
import ExclusiveOffer from '../PropertyAvailability/RoomTypes/RoomTypeList/CheckAvailability/components/ExclusiveOffer';
import useCtaClickEvent from 'hooks/useCtaClickEvent';

const DEFAULT_DATE_FORMAT = 'd MMM, yyyy';

const UppercaseText = styled(Text)`
  text-transform: uppercase;
`;

const Wrapper = styled(Flex)`
  pointer-events: auto;
`;

const OfferButton = styled(Button)`
  &:hover {
    color: ${themeGet('colors.white')};
  }
`;

const OfferDetails = () => {
  const exclusiveOffer = useSelector(getExclusiveOffer);
  const { saleDates, id } = useSelector(getExclusiveOffer) || {};
  const isLoading = useSelector(getIsLoadingExclusiveOffers);
  const leadInOffer = useSelector(getLeadInOffer);
  const { ctaClickEvent } = useCtaClickEvent();

  const { emitInteractionEvent } = useDataLayer();

  if (!leadInOffer) return null;

  const { title, description } = leadInOffer;
  const offerExpiry = saleDates?.end ? format(new Date(saleDates.end as unknown as string | number | Date), DEFAULT_DATE_FORMAT) : '';

  const handleOnClick = () => {
    ctaClickEvent({
      itemText: 'View options',
      itemType: 'button',
      url: `#rooms`,
    });
    emitInteractionEvent({ type: 'Exclusive Offer', value: `View Room Selected for offer ${id}` });

    const el = document.getElementById('rooms') || document.getElementById('check-availability');
    el?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  return (
    <>
      {!isLoading && (
        <Wrapper
          flexDirection="column"
          bg="white"
          mx={exclusiveOffer ? [0, 130, 0] : 0}
          p={exclusiveOffer ? [6, 40, 8] : [3, 3, 8]}
          pt={exclusiveOffer ? [8, 8, 6] : [0, 0, 6]}
          pb={exclusiveOffer ? [3, 0, 4] : [8, 0, 4]}
          width={['initial', 'initial', 490]}
        >
          <Flex borderRadius="default" bg="bayBlue30" px={4} py={1} mb={4} width={230} justifyContent="center" data-testid="offer-expiry">
            <Icon name="schedule" pr={1} />
            <UppercaseText fontSize="xs" fontWeight="bold">
              Offer ends {offerExpiry}
            </UppercaseText>
          </Flex>
          <Text fontSize="lg" mb={2} data-testid="offer-title">
            {title}
          </Text>
          <Text fontSize="base" color="greys.steel" mb={4} data-testid="offer-description">
            {description}
          </Text>
          <ExclusiveOffer
            adults={leadInOffer.adults}
            kids={leadInOffer.children}
            infants={leadInOffer.infants}
            numNights={leadInOffer.minNumberOfNights}
            totalPrice={leadInOffer.offerTotal}
            totalDiscount={leadInOffer.valuedAtTotal}
          />
          <OfferButton width={['100%', '100%', 220]} variant="primary" as={Link} href="#rooms" onClick={handleOnClick}>
            <Text fontSize="base" fontWeight="bold">
              View options
            </Text>
            <Icon name="arrowDownward" pl={1} />
          </OfferButton>
        </Wrapper>
      )}
    </>
  );
};

export default OfferDetails;
