import React from 'react';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import OfferDetails from './OfferDetails';
import { useDataLayer } from 'hooks/useDataLayer';
import { getExclusiveOffer, getIsLoading, getLeadInOffer } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { getQueryPayWith } from 'store/router/routerSelectors';
import { getPointsLevels } from 'store/pointsBurnTiers/pointsBurnSelectors';
import { getLevels } from 'store/pointsConversion/pointsConversionSelectors';
import { getAvailableRoomTypes } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getLuxeInstanceId } from 'store/pointsBurnLuxe/pointsBurnLuxeSelectors';
import useCtaClickEvent from 'hooks/useCtaClickEvent';

jest.mock('hooks/useDataLayer');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('store/pointsBurnTiers/pointsBurnSelectors');
jest.mock('store/pointsConversion/pointsConversionSelectors');
jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');
jest.mock('store/pointsBurnLuxe/pointsBurnLuxeSelectors');
jest.mock('hooks/useCtaClickEvent', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    ctaClickEvent: jest.fn(),
  })),
}));

jest.mock('../PropertyAvailability/RoomTypes/RoomTypeList/CheckAvailability/components/ExclusiveOffer', () => ({
  __esModule: true,
  default: ({ adults, kids, infants, numNights, totalPrice, totalDiscount }) => (
    <div
      data-testid="exclusive-offer"
      data-adults={adults}
      data-kids={kids}
      data-infants={infants}
      data-num-nights={numNights}
      data-total-price={JSON.stringify(totalPrice)}
      data-total-discount={JSON.stringify(totalDiscount)}
    >
      ExclusiveOffer
    </div>
  ),
}));

const mockPointsBurnTiers = [
  {
    min: 0,
    max: 150,
    rate: 0.00854,
  },
  {
    min: 150,
    max: 400,
    rate: 0.00864,
  },
  {
    min: 400,
    max: 650,
    rate: 0.00878,
  },
  {
    min: 650,
    max: 900,
    rate: 0.00907,
  },
  {
    min: 900,
    max: null,
    rate: 0.00964,
  },
];

const getOfferData = (overrides = {}) => ({
  id: '5f1a4e5a-1cf3-4138-940b-b8532356756c',
  rooms: [
    {
      offers: [
        {
          adults: 2,
          children: 0,
          infants: 0,
          description:
            'Complimentary upgrade to Deluxe Sea View room from Deluxe room. Full Buffet Breakfast daily at Backyard Restaurant. Self Parking for one car. Daily free Wifi.',
          minNumberOfNights: 3,
          offerTotal: {
            amount: '499',
            currency: 'AUD',
          },
          title: 'Stay 3 nights and receive super luxe treatment!',
          hasValuedAtPrice: true,
          valuedAtTotal: {
            amount: '899',
            currency: 'AUD',
          },
          id: '5f1a4e5a-1cf3-4138-940b-b8532356756c',
          ...overrides,
        },
      ],
    },
  ],
  saleDates: {
    end: '2021-11-30',
  },
});

const propertyRoomTypes = [
  {
    description: 'room1',
    id: '123',
    offers: [
      {
        id: '345',
        pointsTierInstanceId: 'abcd1234',
      },
      {
        id: '348',
        pointsTierInstanceId: 'dcba1234',
      },
    ],
  },
  {
    description: 'room2',
    id: '124',
    offers: [
      {
        id: '5f1a4e5a-1cf3-4138-940b-b8532356756c',
        pointsTierInstanceId: 'abcd1234',
      },
      {
        id: '349',
        pointsTierInstanceId: 'dcba1234',
      },
    ],
  },
];

const emitInteractionEvent = jest.fn();
const mockCtaClickEvent = jest.fn();

const render = (initialState = {}) => renderWithProviders(<OfferDetails />, { initialState });

describe('<OfferDetails />', () => {
  beforeEach(() => {
    const offerData = getOfferData();
    jest.clearAllMocks();
    (useDataLayer as jest.Mock).mockReturnValue({ emitInteractionEvent });
    (getExclusiveOffer as jest.Mock).mockReturnValue(offerData);
    (getLeadInOffer as unknown as jest.Mock).mockReturnValue(offerData.rooms[0].offers[0]);
    (getQueryPayWith as unknown as jest.Mock).mockReturnValue('cash');
    (getLevels as unknown as jest.Mock).mockReturnValue(mockPointsBurnTiers);
    (getPointsLevels as unknown as jest.Mock).mockReturnValue(mockPointsBurnTiers);
    (getAvailableRoomTypes as jest.Mock).mockReturnValue(propertyRoomTypes);
    (getLuxeInstanceId as jest.Mock).mockReturnValue('abcd1234');
    (useCtaClickEvent as jest.Mock).mockReturnValue({ ctaClickEvent: mockCtaClickEvent });
  });

  describe('when loading', () => {
    beforeEach(() => {
      (getIsLoading as jest.Mock).mockReturnValue(true);
    });

    it('does not render the offer expiry', () => {
      render();
      expect(screen.queryByTestId('offer-expiry')).not.toBeInTheDocument();
    });

    it('does not render the offer title', () => {
      render();
      expect(screen.queryByTestId('offer-title')).not.toBeInTheDocument();
    });

    it('does not render the offer description', () => {
      render();
      expect(screen.queryByTestId('offer-description')).not.toBeInTheDocument();
    });

    it('does not render the offer guests and accommodation duration', () => {
      render();
      expect(screen.queryByTestId('offer-guests')).not.toBeInTheDocument();
    });

    it('does not render the offer price', () => {
      render();
      expect(screen.queryByTestId('exclusive-offer')).not.toBeInTheDocument();
    });
  });

  describe('when not loading', () => {
    beforeEach(() => {
      (getIsLoading as jest.Mock).mockReturnValue(false);
    });

    it('renders the offer expiry', () => {
      render();
      expect(screen.getByTestId('offer-expiry')).toHaveTextContent('Offer ends 30 Nov, 2021');
    });

    it('renders the offer title', () => {
      render();
      expect(screen.getByTestId('offer-title')).toHaveTextContent('Stay 3 nights and receive super luxe treatment!');
    });

    it('renders the offer description', () => {
      render();
      expect(screen.getByTestId('offer-description')).toHaveTextContent(
        'Complimentary upgrade to Deluxe Sea View room from Deluxe room. Full Buffet Breakfast daily at Backyard Restaurant. Self Parking for one car. Daily free Wifi.',
      );
    });

    it('renders the offer pricing', () => {
      render();
      const exclusiveOffer = screen.getByTestId('exclusive-offer');
      expect(exclusiveOffer).toHaveAttribute('data-adults', '2');
      expect(exclusiveOffer).toHaveAttribute('data-kids', '0');
      expect(exclusiveOffer).toHaveAttribute('data-infants', '0');
      expect(exclusiveOffer).toHaveAttribute('data-num-nights', '3');
      expect(exclusiveOffer).toHaveAttribute('data-total-price', '{"amount":"499","currency":"AUD"}');
      expect(exclusiveOffer).toHaveAttribute('data-total-discount', '{"amount":"899","currency":"AUD"}');
    });

    it('emits an event to the data layer when clicked', async () => {
      render();

      const offerButton = screen.getByRole('link', { name: 'View options' });
      await userEvent.click(offerButton);

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Exclusive Offer',
        value: 'View Room Selected for offer 5f1a4e5a-1cf3-4138-940b-b8532356756c',
      });
      expect(mockCtaClickEvent).toHaveBeenCalledWith({
        itemText: 'View options',
        itemType: 'button',
        url: '#rooms',
      });
    });

    describe('scrolling behavior', () => {
      const scrollIntoViewMock = jest.fn();
      window.HTMLElement.prototype.scrollIntoView = scrollIntoViewMock;

      afterEach(() => {
        jest.clearAllMocks();
        document.body.innerHTML = '';
      });

      it('scrolls to the #rooms element when it exists', async () => {
        const roomsElement = document.createElement('div');
        roomsElement.setAttribute('id', 'rooms');
        document.body.appendChild(roomsElement);

        render();
        const offerButton = screen.getByRole('link', { name: 'View options' });

        await userEvent.click(offerButton);

        expect(scrollIntoViewMock).toHaveBeenCalledWith({ behavior: 'smooth', block: 'start' });
        expect(scrollIntoViewMock).toHaveBeenCalledTimes(1);
      });

      it('scrolls to #check-availability if #rooms does not exist', async () => {
        const checkAvailabilityElement = document.createElement('div');
        checkAvailabilityElement.setAttribute('id', 'check-availability');
        document.body.appendChild(checkAvailabilityElement);

        render();
        const offerButton = screen.getByRole('link', { name: 'View options' });

        await userEvent.click(offerButton);

        expect(scrollIntoViewMock).toHaveBeenCalledWith({ behavior: 'smooth', block: 'start' });
        expect(scrollIntoViewMock).toHaveBeenCalledTimes(1);
      });
    });
  });
});
