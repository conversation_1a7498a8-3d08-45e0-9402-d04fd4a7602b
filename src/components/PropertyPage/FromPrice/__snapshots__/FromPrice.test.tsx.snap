// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<FromPrice/> PriceBeforeDiscount when isPointsRedemptionStrikethrough is true paywith is points, isLuxuryOffer is false, has discount and offer is not classic should render the correct price before discount 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="css-1ea11h3-Box e1m6xhuh0"
      >
        <div
          data-testid="result-loader"
        >
          <div
            class="css-1pifoiw-Box e1m6xhuh0"
          >
            <div
              class="css-lxnltf-Box-Flex-Wrapper e1tk5xz70"
              data-testid="from-price-box"
            >
              <div
                class="css-zdo97n-Box-Flex-DataWrapper e1tk5xz71"
              >
                <div
                  class="css-yy0ywv-Box-Flex e1pfwvfi0"
                >
                  <div
                    class="css-xos6e5-Box-NightsAndGuestsWrapper e1tk5xz73"
                    data-testid="nights-guests-new"
                  >
                    <span
                      class="css-y199ls-Text e1j4w3aq0"
                    >
                       
                      3
                       guests  •
                       
                    </span>
                    <span
                      class="css-l5bqwt-Text-OfferBreakdownText emqlpr20"
                      data-testid="numberOfNights"
                    >
                      2 nights
                    </span>
                    <span
                      class="css-y199ls-Text e1j4w3aq0"
                    >
                       
                      from
                    </span>
                  </div>
                  <div
                    class="css-11uqv3h-Box-Flex e1pfwvfi0"
                    data-testid="currency-box"
                  >
                    <div
                      class="css-18njgpj-Box-Wrapper e1c6pi2o0"
                      data-testid="total-to-pay"
                    >
                      <div
                        class="css-1szwv6i-Box-Flex e1pfwvfi0"
                      >
                        <span
                          class="css-1msd0jo-Amount e1c6pi2o1"
                          data-testid="amount"
                          font-size="32"
                          text-decoration="none"
                        >
                          800
                        </span>
                      </div>
                    </div>
                    <span
                      class="css-3ciq34-Text e1j4w3aq0"
                    >
                      PTS
                      <sup>
                        *
                      </sup>
                    </span>
                    <div
                      class="css-1gi57j3-Box e1m6xhuh0"
                    >
                      <div
                        data-amount="900"
                        data-testid="PriceBeforeDiscount"
                      >
                        900
                      </div>
                    </div>
                    <div
                      class="css-1gi57j3-Box e1m6xhuh0"
                    >
                      <div
                        data-testid="points-redemption-sash"
                        style="background-color: green; color: white;"
                      >
                        Save 100 PTS
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="css-17nolui-Box-Flex e1pfwvfi0"
              >
                <a
                  aria-label="View rooms"
                  class="e1tk5xz72 css-1j3xxbb-Link-Button-ButtonLink e1ou0xre0"
                  data-testid="view-rooms"
                  href="#view-rooms"
                >
                  View Rooms
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div
        class="css-1ea11h3-Box e1m6xhuh0"
      >
        <div
          data-testid="result-loader"
        >
          <div
            class="css-1pifoiw-Box e1m6xhuh0"
          >
            <div
              class="css-lxnltf-Box-Flex-Wrapper e1tk5xz70"
              data-testid="from-price-box"
            >
              <div
                class="css-zdo97n-Box-Flex-DataWrapper e1tk5xz71"
              >
                <div
                  class="css-yy0ywv-Box-Flex e1pfwvfi0"
                >
                  <div
                    class="css-xos6e5-Box-NightsAndGuestsWrapper e1tk5xz73"
                    data-testid="nights-guests-new"
                  >
                    <span
                      class="css-y199ls-Text e1j4w3aq0"
                    >
                       
                      3
                       guests  •
                       
                    </span>
                    <span
                      class="css-l5bqwt-Text-OfferBreakdownText emqlpr20"
                      data-testid="numberOfNights"
                    >
                      2 nights
                    </span>
                    <span
                      class="css-y199ls-Text e1j4w3aq0"
                    >
                       
                      from
                    </span>
                  </div>
                  <div
                    class="css-11uqv3h-Box-Flex e1pfwvfi0"
                    data-testid="currency-box"
                  >
                    <div
                      class="css-18njgpj-Box-Wrapper e1c6pi2o0"
                      data-testid="total-to-pay"
                    >
                      <div
                        class="css-1szwv6i-Box-Flex e1pfwvfi0"
                      >
                        <span
                          class="css-1msd0jo-Amount e1c6pi2o1"
                          data-testid="amount"
                          font-size="32"
                          text-decoration="none"
                        >
                          800
                        </span>
                      </div>
                    </div>
                    <span
                      class="css-3ciq34-Text e1j4w3aq0"
                    >
                      PTS
                      <sup>
                        *
                      </sup>
                    </span>
                    <div
                      class="css-1gi57j3-Box e1m6xhuh0"
                    >
                      <div
                        data-amount="900"
                        data-testid="PriceBeforeDiscount"
                      >
                        900
                      </div>
                    </div>
                    <div
                      class="css-1gi57j3-Box e1m6xhuh0"
                    >
                      <div
                        data-testid="points-redemption-sash"
                        style="background-color: green; color: white;"
                      >
                        Save 100 PTS
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="css-17nolui-Box-Flex e1pfwvfi0"
              >
                <a
                  aria-label="View rooms"
                  class="e1tk5xz72 css-1j3xxbb-Link-Button-ButtonLink e1ou0xre0"
                  data-testid="view-rooms"
                  href="#view-rooms"
                >
                  View Rooms
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="css-1ea11h3-Box e1m6xhuh0"
    >
      <div
        data-testid="result-loader"
      >
        <div
          class="css-1pifoiw-Box e1m6xhuh0"
        >
          <div
            class="css-lxnltf-Box-Flex-Wrapper e1tk5xz70"
            data-testid="from-price-box"
          >
            <div
              class="css-zdo97n-Box-Flex-DataWrapper e1tk5xz71"
            >
              <div
                class="css-yy0ywv-Box-Flex e1pfwvfi0"
              >
                <div
                  class="css-xos6e5-Box-NightsAndGuestsWrapper e1tk5xz73"
                  data-testid="nights-guests-new"
                >
                  <span
                    class="css-y199ls-Text e1j4w3aq0"
                  >
                     
                    3
                     guests  •
                     
                  </span>
                  <span
                    class="css-l5bqwt-Text-OfferBreakdownText emqlpr20"
                    data-testid="numberOfNights"
                  >
                    2 nights
                  </span>
                  <span
                    class="css-y199ls-Text e1j4w3aq0"
                  >
                     
                    from
                  </span>
                </div>
                <div
                  class="css-11uqv3h-Box-Flex e1pfwvfi0"
                  data-testid="currency-box"
                >
                  <div
                    class="css-18njgpj-Box-Wrapper e1c6pi2o0"
                    data-testid="total-to-pay"
                  >
                    <div
                      class="css-1szwv6i-Box-Flex e1pfwvfi0"
                    >
                      <span
                        class="css-1msd0jo-Amount e1c6pi2o1"
                        data-testid="amount"
                        font-size="32"
                        text-decoration="none"
                      >
                        800
                      </span>
                    </div>
                  </div>
                  <span
                    class="css-3ciq34-Text e1j4w3aq0"
                  >
                    PTS
                    <sup>
                      *
                    </sup>
                  </span>
                  <div
                    class="css-1gi57j3-Box e1m6xhuh0"
                  >
                    <div
                      data-amount="900"
                      data-testid="PriceBeforeDiscount"
                    >
                      900
                    </div>
                  </div>
                  <div
                    class="css-1gi57j3-Box e1m6xhuh0"
                  >
                    <div
                      data-testid="points-redemption-sash"
                      style="background-color: green; color: white;"
                    >
                      Save 100 PTS
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="css-17nolui-Box-Flex e1pfwvfi0"
            >
              <a
                aria-label="View rooms"
                class="e1tk5xz72 css-1j3xxbb-Link-Button-ButtonLink e1ou0xre0"
                data-testid="view-rooms"
                href="#view-rooms"
              >
                View Rooms
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "store": {
    "clearActions": [Function],
    "dispatch": [MockFunction],
    "getActions": [Function],
    "getState": [Function],
    "replaceReducer": [Function],
    "subscribe": [Function],
  },
  "unmount": [Function],
}
`;
