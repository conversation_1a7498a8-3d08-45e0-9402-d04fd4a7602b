import React from 'react';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import FromPrice from './FromPrice';
import { useDataLayer } from 'hooks/useDataLayer';
import { getAvailableRoomTypes, getHasValidQuery, getIsLoading } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getQueryCheckIn, getQueryCheckOut, getQueryAdults, getQueryChildren, getQueryInfants } from 'store/router/routerSelectors';
import { useBreakpoints } from 'hooks/useBreakpoints';
import useCtaClickEvent from 'hooks/useCtaClickEvent';
import {
  availableRoomTypes,
  availableRoomTypesPoints,
  availableRoomTypesPointsNoDiscount,
  availableRoomTypesClassic,
  availableRoomTypesPointsLuxOffer,
} from './constants';
import { mocked } from 'test-utils';
import { renderWithProviders } from 'test-utils/reactUtils';
import ResultLoader from 'components/Loader/ResultLoader';
import usePointsRedemptionStrikethrough from 'hooks/optimizely/usePointsRedemptionStrikethrough';

jest.mock('hooks/useDataLayer');
jest.mock('store/router/routerSelectors');
jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');
jest.mock('components/Loader/ResultLoader', () => jest.fn(({ children }) => <div data-testid="result-loader">{children}</div>));
jest.mock('store/campaign/campaignSelectors');
jest.mock('hooks/useBreakpoints', () => ({ useBreakpoints: jest.fn() }));
jest.mock('hooks/useCtaClickEvent', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    ctaClickEvent: jest.fn(),
  })),
}));
jest.mock('hooks/optimizely/usePointsRedemptionStrikethrough', () => ({
  __esModule: true,
  default: jest.fn(),
}));
jest.mock('components/PointsRedemptionSash', () => {
  return function MockPointsRedemptionSash({ campaignMessage, backgroundColor, color }) {
    return (
      <div data-testid="points-redemption-sash" style={{ backgroundColor, color }}>
        {campaignMessage}
      </div>
    );
  };
});

// eslint-disable-next-line @typescript-eslint/no-unused-vars
jest.mock('components/CampaignPriceMessage', () => ({ children, currency, offerType, fallback, ...restProps }) => (
  <div data-testid="campaign-price-message" data-currency={currency} data-offer-type={offerType}>
    {children}
  </div>
));

const filterDomProps = (props: Record<string, unknown>, additionalStyleProps: string[] = []) => {
  const defaultStylingProps: string[] = [
    'color',
    'pt',
    'pb',
    'pl',
    'pr',
    'p',
    'm',
    'mt',
    'ml',
    'mr',
    'mb',
    'fontSize',
    'lineHeight',
    'display',
  ];

  const allStylingProps = [...defaultStylingProps, ...additionalStyleProps];

  return Object.keys(props).reduce(
    (acc, key) => {
      if (!allStylingProps.includes(key)) {
        acc[key] = props[key];
      }
      return acc;
    },
    {} as Record<string, unknown>,
  );
};

jest.mock('components/PriceBeforeDiscount', () => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  return function MockPriceBeforeDiscount({ total, discount, showMessage, ...props }) {
    const amount = total?.amount ?? discount?.amount ?? '';
    const domProps = filterDomProps(props, [
      'roundToCeiling',
      'lineHeight',
      'offerType',
      'fontSize',
      'color',
      'fontWeight',
      'textDecoration',
      'hideCurrency',
    ]);
    return (
      <div data-testid="PriceBeforeDiscount" data-amount={amount} {...domProps}>
        {amount}
      </div>
    );
  };
});

const mockCtaClickEvent = jest.fn();
const emitInteractionEvent = jest.fn();
const mockedUsePointsRedemptionStrikethrough = mocked(usePointsRedemptionStrikethrough);

const checkIn = new Date(2025, 3, 10);
const checkOut = new Date(2025, 3, 12);

beforeEach(() => {
  jest.clearAllMocks();
  mocked(getQueryCheckIn).mockReturnValue(checkIn);
  mocked(getQueryCheckOut).mockReturnValue(checkOut);
  mocked(getQueryAdults).mockReturnValue(2);
  mocked(getQueryChildren).mockReturnValue(1);
  mocked(getQueryInfants).mockReturnValue(0);
  mocked(getHasValidQuery).mockReturnValue(true);
  mocked(getAvailableRoomTypes).mockReturnValue(availableRoomTypes);
  mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => false });
  mocked(useCtaClickEvent).mockReturnValue({ ctaClickEvent: mockCtaClickEvent });
  mockedUsePointsRedemptionStrikethrough.mockReturnValue({ isReady: true, isPointsRedemptionStrikethrough: false });
});

describe('<FromPrice/>', () => {
  describe('when loading', () => {
    beforeEach(() => {
      mocked(getIsLoading).mockReturnValue(true);
    });

    it('renders the ResultLoader with correct props', () => {
      renderWithProviders(<FromPrice />);
      expect(screen.getByTestId('result-loader')).toBeInTheDocument();
      expect(ResultLoader).toHaveBeenCalledWith(
        expect.objectContaining({
          isLoading: true,
          skeletonResultCount: 1,
          skeletonCardComponent: expect.any(Object),
        }),
        {},
      );
    });
  });

  describe('when not loading', () => {
    beforeEach(() => {
      mocked(getIsLoading).mockReturnValue(false);
    });

    it('renders the FromPriceBox', () => {
      renderWithProviders(<FromPrice />);
      expect(screen.getByTestId('from-price-box')).toBeInTheDocument();
    });

    it('shows the number of nights', () => {
      renderWithProviders(<FromPrice />);
      expect(screen.getByText('2 nights')).toBeInTheDocument();
    });

    describe('Currency is cash', () => {
      it('shows the currency after the number of nights', () => {
        renderWithProviders(<FromPrice />);
        expect(screen.getByTestId('currency-symbol')).toBeInTheDocument();
      });

      it('shows the price', () => {
        renderWithProviders(<FromPrice />);
        expect(screen.getByTestId('total-to-pay')).toHaveTextContent('$540');
        expect(screen.getByTestId('currency-symbol')).toHaveTextContent('$');
        expect(screen.getByTestId('amount')).toHaveTextContent('540');
      });

      it('shows the currency without the *', () => {
        renderWithProviders(<FromPrice />);
        expect(screen.getByText('AUD')).toBeInTheDocument();
        expect(screen.queryByText('PTS*')).not.toBeInTheDocument();
      });
    });

    describe('when the currency is PTS', () => {
      beforeEach(() => {
        mocked(getAvailableRoomTypes).mockReturnValue(availableRoomTypesPoints);
      });

      it('shows the * if the currency is points', () => {
        renderWithProviders(<FromPrice />);

        expect(screen.getByTestId('currency-box')).toHaveTextContent('PTS*');
      });
    });

    describe('View Room button', () => {
      it('renders the View Rooms button with the correct link', () => {
        renderWithProviders(<FromPrice />);
        const viewRoomsButton = screen.getByRole('link', { name: /view rooms/i });
        expect(viewRoomsButton).toBeInTheDocument();
        expect(viewRoomsButton).toHaveAttribute('href', '#view-rooms');
      });

      it('dispatches an event to the data layer when clicked', async () => {
        renderWithProviders(<FromPrice />);
        const viewRoomsButton = screen.getByRole('link', { name: /view rooms/i });

        await userEvent.click(viewRoomsButton);

        expect(emitInteractionEvent).toHaveBeenCalledWith({
          type: 'From Price',
          value: 'View Room Selected',
        });
        expect(mockCtaClickEvent).toHaveBeenCalledWith({
          itemText: 'View Rooms',
          itemType: 'button',
          url: '#view-rooms',
        });
      });

      it('does not render for mobile', () => {
        mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => true });
        renderWithProviders(<FromPrice />);
        expect(screen.queryByRole('link', { name: /view rooms/i })).not.toBeInTheDocument();
      });
    });

    describe('if there is no availability', () => {
      beforeEach(() => {
        mocked(getAvailableRoomTypes).mockReturnValue([]);
      });
      it('does not render the FromPriceBox', () => {
        renderWithProviders(<FromPrice />);
        expect(screen.queryByTestId('from-price-box')).not.toBeInTheDocument();
      });
    });
  });

  describe('when the query is not valid', () => {
    beforeEach(() => {
      mocked(getHasValidQuery).mockReturnValue(false);
    });

    it('does NOT render if it is mobile', () => {
      mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => true });
      renderWithProviders(<FromPrice />);
      expect(screen.queryByTestId('from-price-box')).not.toBeInTheDocument();
    });

    it('does NOT render FromPriceBox but renders CheckAvailabilityButton when not mobile', () => {
      mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => false });
      renderWithProviders(<FromPrice />);
      expect(screen.queryByTestId('from-price-box')).not.toBeInTheDocument();
      expect(screen.getByRole('button', { name: /check availability/i })).toBeInTheDocument();
    });
  });

  describe('PriceBeforeDiscount when isPointsRedemptionStrikethrough is true', () => {
    beforeEach(() => {
      mockedUsePointsRedemptionStrikethrough.mockReturnValue({ isReady: true, isPointsRedemptionStrikethrough: true });
    });

    describe('paywith is points, isLuxuryOffer is false, has discount and offer is not classic', () => {
      beforeEach(() => {
        mocked(getAvailableRoomTypes).mockReturnValue(availableRoomTypesPoints);
      });

      it('should render the correct price before discount', () => {
        renderWithProviders(<FromPrice />);
        expect(screen.getByTestId('PriceBeforeDiscount')).toHaveTextContent('900');
        expect(renderWithProviders(<FromPrice />)).toMatchSnapshot();
      });
    });

    describe('paywith is points, isLuxuryOffer is false, offer is not classic but there is no discount', () => {
      beforeEach(() => {
        mocked(getAvailableRoomTypes).mockReturnValue(availableRoomTypesPointsNoDiscount);
      });

      it('should not render the price before discount', () => {
        renderWithProviders(<FromPrice />);
        expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
      });
    });

    describe('paywith is points, isLuxuryOffer is false, hasDiscount is true and offer type is classic', () => {
      beforeEach(() => {
        mocked(getAvailableRoomTypes).mockReturnValue(availableRoomTypesClassic);
      });

      it('should not render the price before discount', () => {
        renderWithProviders(<FromPrice />);
        expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
      });
    });

    describe('paywith is cash, hasDiscount is true, isLuxuryOffer is false, and offer type is not classic', () => {
      beforeEach(() => {
        mocked(getAvailableRoomTypes).mockReturnValue(availableRoomTypes);
      });

      it('should not render the price before discount', () => {
        renderWithProviders(<FromPrice />);
        expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
      });
    });

    describe('paywith is points, hasDiscount is true and offer type is not classic but isLuxuryOffer is true', () => {
      beforeEach(() => {
        mocked(getAvailableRoomTypes).mockReturnValue(availableRoomTypesPointsLuxOffer);
      });

      it('should not render the price before discount', () => {
        renderWithProviders(<FromPrice />);
        expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
      });
    });

    describe('PointsRedemptionSash when showPointsRedemptionStrikethrough is true', () => {
      beforeEach(() => {
        mocked(getAvailableRoomTypes).mockReturnValue(availableRoomTypesPoints);
      });

      it('renders the PointsRedemptionSash with the correct props', () => {
        renderWithProviders(<FromPrice />);
        expect(screen.getByTestId('points-redemption-sash')).toBeInTheDocument();
        expect(screen.getByTestId('points-redemption-sash')).toHaveTextContent('Save 100 PTS');
      });
    });
  });

  describe('PriceBeforeDiscount when isPointsRedemptionStrikethrough is false', () => {
    describe('when paywith is points, hasDiscount is true, isLuxuryOffer is false and offer is not classic', () => {
      beforeEach(() => {
        mockedUsePointsRedemptionStrikethrough.mockReturnValue({ isReady: true, isPointsRedemptionStrikethrough: false });
        mocked(getAvailableRoomTypes).mockReturnValue(availableRoomTypesPoints);
      });

      it('should not render the price before discount', () => {
        renderWithProviders(<FromPrice />);
        expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
      });

      it('renders the CampaignPriceMessage with the correct props', () => {
        renderWithProviders(<FromPrice />);
        expect(screen.getByTestId('campaign-price-message')).toBeInTheDocument();
        expect(screen.getByTestId('campaign-price-message')).toHaveAttribute('data-currency', 'PTS');
        expect(screen.getByTestId('campaign-price-message')).toHaveAttribute('data-offer-type', 'special');
      });
    });
  });
});
