import React, { useCallback } from 'react';
import { Box, Flex, Icon, NakedButton } from '@qga/roo-ui/components';
import get from 'lodash/get';
import { OfferWrapper } from '../primitives';
import ExpandedClickableArea from 'components/ExpandedClickableArea';
import PromotionalSashNew from 'components/PromotionalSashNew';
import PromotionalSashRedVariant from 'components/PromotionalSashRedVariant';
import { useDataLayer } from 'hooks/useDataLayer';
import type { Offer } from 'types/property';
import { VALUE_ADD_SASH } from 'config/constants';
import OfferDescription from './components/OfferDescription';
import OfferPricing from './components/OfferPricing';
import { useShowRedSashToggle } from 'hooks/useShowRedSashToggle';

interface OfferSummaryProps {
  offer: Offer;
  toggleOfferExpanded: (value: boolean) => void;
}

const CollapsedOffer = ({ offer, toggleOfferExpanded }: OfferSummaryProps) => {
  const {
    allocationsAvailable,
    name: offerName,
    charges,
    inclusions,
    cancellationPolicy,
    depositPay,
    luxOffer,
    valueAdds = [],
    type,
  } = offer;
  const promotionName = get(offer, 'promotion.name') || '';
  const { emitInteractionEvent } = useDataLayer();
  const isClassic = promotionName === 'Classic Reward';
  const sashPromotionName = promotionName || VALUE_ADD_SASH;
  const showPromotionalSashNew = promotionName || valueAdds?.length > 0;
  const { isReady, showRedSash } = useShowRedSashToggle({ promotionName });

  const onExpandOffer = useCallback(() => {
    toggleOfferExpanded(true);
    emitInteractionEvent({ type: 'Room Offer Details', value: 'Offer Expanded' });
  }, [toggleOfferExpanded, emitInteractionEvent]);

  return (
    <ExpandedClickableArea>
      <OfferWrapper flexDirection="column" data-testid="offer-card-new">
        {showPromotionalSashNew && (
          <Box position="absolute" left={0} top={0}>
            {isReady && showRedSash ? (
              <PromotionalSashRedVariant promotionName={sashPromotionName} type="corner" />
            ) : (
              <PromotionalSashNew promotionName={sashPromotionName} type="corner" />
            )}
          </Box>
        )}
        <Flex data-testid="expand-icon" flexDirection={'row'} justifyContent="flex-end">
          <NakedButton
            onClick={onExpandOffer}
            aria-label="Expand offer details"
            data-expanded-clickable-area-target
            p={0}
            data-testid="expand-offer-summary"
          >
            <Icon name="expandMore" size={24} />
          </NakedButton>
        </Flex>
        <Flex data-testid="offer-card-body" flexDirection={['column', 'row', 'row']} width="100%" flexWrap="wrap">
          <OfferDescription offerName={offerName} inclusions={inclusions} valueAdds={valueAdds} onClick={onExpandOffer} />
          <OfferPricing
            isClassic={isClassic}
            charges={charges}
            allocationsAvailable={allocationsAvailable}
            cancellationPolicy={cancellationPolicy}
            depositPay={depositPay}
            offerType={type}
            isLuxuryOffer={!!luxOffer}
          />
        </Flex>
      </OfferWrapper>
    </ExpandedClickableArea>
  );
};

export default CollapsedOffer;
