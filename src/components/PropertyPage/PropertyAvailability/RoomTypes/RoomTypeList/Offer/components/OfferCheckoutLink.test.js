import React from 'react';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import OfferCheckoutLink from './OfferCheckoutLink';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import { getFullKnownQuery } from 'store/router/routerSelectors';
import { getAllAvailableOffers } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { getRoomTypesWithoutOffers, getProperty } from 'store/property/propertySelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import { useRouter } from 'next/router';
import { getAvailableRoomTypes } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { GetShowCtaMessage } from 'lib/analytics/eventsMap/helpers/GetShowCtaMessage';
import useCtaClickEvent from 'hooks/useCtaClickEvent';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough';
import useSelectPromotionEvent from 'hooks/useSelectPromotionEvent';

jest.mock('store/router/routerSelectors');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');
jest.mock('store/pointsBurnTiers/pointsBurnSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('hooks/useSelectPromotionEvent');
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));
jest.mock('lib/analytics/eventsMap/helpers/GetShowCtaMessage', () => ({
  GetShowCtaMessage: jest.fn(),
}));
jest.mock('hooks/useCtaClickEvent', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    ctaClickEvent: jest.fn(),
  })),
}));
jest.mock('hooks/optimizely/usePriceStrikethrough', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    isPriceStrikethrough: false,
  })),
}));
jest.mock('components/CheckoutPage/BookingErrorDialogs/MinimumPointsDialog', () => {
  return function MinimumPointsDialog({ isOpen, onContinueClick, onChangeClick }) {
    if (!isOpen) return null;
    return (
      <div>
        <div>minimum points</div>
        <button onClick={onContinueClick}>Continue</button>
        <button onClick={onChangeClick}>Change</button>
      </div>
    );
  };
});

const defaultProps = {
  children: 'Book now',
  propertyId: '1',
  roomTypeId: '2',
  offerId: '3',
  offerName: 'Best Rate',
  roomName: 'Standard King',
};

const query = {
  checkIn: new Date(2020, 10, 10),
  checkOut: new Date(2020, 10, 11),
  payWith: 'cash',
  exclusiveOffer: false,
};

const mockRouter = {
  query,
  push: jest.fn(),
};

const mockAvailableOffers = {
  3: {
    allocationsAvailable: 1,
    id: '3',
    charges: {
      payableAtBooking: { total: { amount: 1553, currency: 'PTS' } },
      strikethrough: { total: { amount: 2000, currency: 'PTS' } },
    },
  },
};

const mockRoomTypes = [
  {
    id: '2',
    name: 'Standard Double Room',
    mainImage: {
      caption: 'Room',
      urlMedium: 'https://i.travelapi.com/lodging/37000000/36840000/36833100/36833039/24c34abe_b.jpg',
    },
  },
];

const mockProperty = {
  id: '1234',
  name: 'Mock Hotel',
  category: 'hotels',
  rating: 4,
  address: {
    state: 'New South Wales',
    country: 'Australia',
  },
};

const mockCtaClickEvent = jest.fn();
const mockFireSelectPromotionEvent = jest.fn();
const emitInteractionEvent = jest.fn();

const render = (props = {}) => {
  const initialState = {};
  return renderWithProviders(<OfferCheckoutLink {...defaultProps} {...props} />, initialState);
};

const getOfferButton = () => screen.getByTestId('offer-checkout-link');

const clickButton = async () => {
  const button = getOfferButton();
  button.addEventListener('click', (e) => e.preventDefault());
  await userEvent.click(button);
  return button;
};

const getExpectedOfferWithoutStrikethrough = () => {
  const expectedOffer = { ...mockAvailableOffers[3] };
  delete expectedOffer.charges.strikethrough;
  return expectedOffer;
};

const getExpectedAddToCartPayload = (overrides = {}) => ({
  type: 'checkout/ADD_TO_CART',
  payload: {
    ctaMessage: '',
    ctaMessageCategory: '',
    initialCash: null,
    isRebooked: false,
    offer: getExpectedOfferWithoutStrikethrough(),
    pointsConversion: undefined,
    property: mockProperty,
    query,
    roomType: mockRoomTypes[0],
    ...overrides,
  },
});

beforeEach(() => {
  jest.clearAllMocks();
  getFullKnownQuery.mockReturnValue(query);
  getAllAvailableOffers.mockReturnValue(mockAvailableOffers);
  getRoomTypesWithoutOffers.mockReturnValue(mockRoomTypes);
  getAvailableRoomTypes.mockReturnValue(mockRoomTypes);
  getProperty.mockReturnValue(mockProperty);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  useRouter.mockReturnValue(mockRouter);
  GetShowCtaMessage.mockReturnValue(false);
  useCtaClickEvent.mockReturnValue({ ctaClickEvent: mockCtaClickEvent });
  usePriceStrikethrough.mockReturnValue({ showFullTraffic: false, showVariationB: false });
  useSelectPromotionEvent.mockReturnValue({ fireSelectPromotionEvent: mockFireSelectPromotionEvent });

  Object.defineProperty(window, 'location', {
    value: {
      assign: jest.fn(),
    },
    writable: true,
  });
});

describe('when clicking the link', () => {
  it('sends an event to the data layer', async () => {
    render();
    await clickButton();

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Select Offer Button',
      value: 'Best Rate',
    });
  });

  it('fires select promotion event', async () => {
    render();
    await clickButton();

    expect(mockFireSelectPromotionEvent).toHaveBeenCalled();
  });

  it('emits the ctaClickEvent', async () => {
    render();
    await clickButton();

    expect(mockCtaClickEvent).toHaveBeenCalledWith({
      itemText: 'Book now',
      itemType: 'button',
      url: '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&offerId=3&payWith=cash&propertyId=1&roomTypeId=2',
    });
  });

  it('dispatches addToCart with correct parameters when isPriceStrikethrough is false', async () => {
    const { store } = render();

    await clickButton();

    expect(store.dispatch).toHaveBeenCalledWith(getExpectedAddToCartPayload());
  });

  it('retains strikethrough in offerToDispatch when isPriceStrikethrough is true', async () => {
    usePriceStrikethrough.mockReturnValue({ showFullTraffic: true, showVariationB: false });
    const { store } = render();

    await clickButton();

    expect(store.dispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        payload: expect.objectContaining({
          offer: mockAvailableOffers[3],
        }),
      }),
    );
  });

  describe('with showCtaMessage true', () => {
    beforeEach(() => {
      GetShowCtaMessage.mockReturnValue(true);
    });

    it('dispatches addToCart with the correct CTA message', async () => {
      const { store } = render();
      await clickButton();

      expect(store.dispatch).toHaveBeenCalledWith(
        getExpectedAddToCartPayload({
          ctaMessage: 'Hurry, we only have 1 room left!',
          ctaMessageCategory: 'available rooms',
        }),
      );
    });
  });

  describe('with rebook action', () => {
    beforeEach(() => {
      mockRouter.query.ss_action = 'rebook';
    });

    afterEach(() => {
      delete mockRouter.query.ss_action;
    });

    it('sets isRebooked to true in addToCart payload', async () => {
      const { store } = render();
      await clickButton();

      expect(store.dispatch).toHaveBeenCalledWith(getExpectedAddToCartPayload({ isRebooked: true }));
    });
  });
});

describe('minimum points dialog', () => {
  beforeEach(() => {
    getAllAvailableOffers.mockReturnValue({
      3: {
        ...mockAvailableOffers[3],
        charges: {
          total: { amount: 1000, currency: 'PTS' },
        },
      },
    });
  });

  it('opens minimum points dialog when offer amount is below minimum', async () => {
    render();
    await clickButton();

    expect(screen.getByText(/minimum points/i)).toBeInTheDocument();
  });

  it('does not open dialog when offer amount meets minimum', async () => {
    getAllAvailableOffers.mockReturnValue({
      3: {
        ...mockAvailableOffers[3],
        charges: {
          total: { amount: 10000, currency: 'PTS' },
        },
      },
    });

    render();
    await clickButton();

    expect(screen.queryByText(/minimum points/i)).not.toBeInTheDocument();
  });

  it('does not open dialog for non-points currency', async () => {
    getAllAvailableOffers.mockReturnValue({
      3: {
        ...mockAvailableOffers[3],
        charges: {
          total: { amount: 100, currency: 'AUD' },
        },
      },
    });

    render();
    await clickButton();

    expect(screen.queryByText(/minimum points/i)).not.toBeInTheDocument();
  });

  it('continues to checkout when continue button is clicked', async () => {
    render();
    await clickButton();

    const continueButton = screen.getByText(/continue/i);
    await userEvent.click(continueButton);

    expect(mockRouter.push).toHaveBeenCalledWith(
      '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&offerId=3&payWith=cash&propertyId=1&roomTypeId=2',
    );
  });

  it('closes dialog when change button is clicked', async () => {
    render();
    await clickButton();

    const changeButton = screen.getByText(/change/i);
    await userEvent.click(changeButton);

    expect(screen.queryByText(/minimum points/i)).not.toBeInTheDocument();
  });
});

describe('href generation', () => {
  const expectHref = (expectedHref) => {
    expect(screen.getByRole('link')).toHaveAttribute('href', expectedHref);
  };

  it('renders href without initialCash when not provided', () => {
    render();
    expectHref('/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&offerId=3&payWith=cash&propertyId=1&roomTypeId=2');
  });

  it('renders href with payWith from query when not overridden', () => {
    render();
    expectHref('/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&offerId=3&payWith=cash&propertyId=1&roomTypeId=2');
  });

  it('includes ss_action in href when present in router query', () => {
    mockRouter.query.ss_action = 'rebook';
    render();
    expectHref(
      '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&offerId=3&payWith=cash&propertyId=1&roomTypeId=2&ss_action=rebook',
    );
    delete mockRouter.query.ss_action;
  });

  it('includes exclusiveOffer in href when true', () => {
    render({ exclusiveOffer: true });
    expectHref('/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=true&offerId=3&payWith=cash&propertyId=1&roomTypeId=2');
  });

  describe('with initialCash', () => {
    it('includes initialCash in href when provided', () => {
      getFullKnownQuery.mockReturnValue({
        checkIn: new Date(2020, 10, 10),
        checkOut: new Date(2020, 10, 11),
        payWith: 'points',
      });

      render({ initialCash: 100 });
      expectHref(
        '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&initialCash=100&offerId=3&payWith=points&propertyId=1&roomTypeId=2',
      );
    });

    it('includes initialCash with cash payWith', () => {
      render({ initialCash: 100 });
      expectHref(
        '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&initialCash=100&offerId=3&payWith=cash&propertyId=1&roomTypeId=2',
      );
    });
  });

  describe('with points payWith', () => {
    beforeEach(() => {
      getFullKnownQuery.mockReturnValue({
        checkIn: new Date(2020, 10, 10),
        checkOut: new Date(2020, 10, 11),
        payWith: 'points',
      });
    });

    it('excludes initialCash when not provided', () => {
      render();
      expectHref(
        '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&offerId=3&payWith=points&propertyId=1&roomTypeId=2',
      );
    });
  });

  describe('with custom payWith prop', () => {
    it('uses payWith prop over query payWith', () => {
      render({ payWith: 'points' });
      expectHref(
        '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&offerId=3&payWith=points&propertyId=1&roomTypeId=2',
      );
    });
  });
});

describe('component rendering', () => {
  it('renders with correct aria-label', () => {
    render();
    const button = getOfferButton();
    expect(button).toHaveAttribute('aria-label', 'Select Standard King, Best Rate');
  });

  it('renders with custom children', () => {
    render({ children: 'Custom Button Text' });
    expect(screen.getByText('Custom Button Text')).toBeInTheDocument();
  });

  it('renders with default children when not provided', () => {
    render({ children: undefined });
    expect(screen.getByText('Select')).toBeInTheDocument();
  });
});

describe('edge cases', () => {
  it('handles missing offer gracefully', () => {
    getAllAvailableOffers.mockReturnValue({});
    render();
    expect(getOfferButton()).toBeInTheDocument();
  });

  it('handles missing room type gracefully', () => {
    getAvailableRoomTypes.mockReturnValue([]);
    render();
    expect(getOfferButton()).toBeInTheDocument();
  });

  it('handles offer without total charges', async () => {
    getAllAvailableOffers.mockReturnValue({
      3: {
        ...mockAvailableOffers[3],
        charges: {
          payableAtBooking: {},
        },
      },
    });

    render();
    await clickButton();

    expect(screen.queryByText(/minimum points/i)).not.toBeInTheDocument();
  });

  it('handles offer with null charges', async () => {
    getAllAvailableOffers.mockReturnValue({
      3: {
        ...mockAvailableOffers[3],
        charges: null,
      },
    });

    render();
    await clickButton();

    expect(screen.queryByText(/minimum points/i)).not.toBeInTheDocument();
  });
});
