/* eslint-disable no-unused-vars */
import React from 'react';
import { screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import OfferPriceBox from './OfferPriceBox';
import { getLevels } from 'store/pointsConversion/pointsConversionSelectors';
import { getPointsLevels } from 'store/pointsBurnTiers/pointsBurnSelectors';
import { TIER_9 } from 'test-utils/points/conversionTiers';
import { getQueryCheckIn, getQueryCheckOut, getQueryAdults, getQueryChildren, getQueryInfants } from 'store/router/routerSelectors';
import { getPropertyId } from 'store/property/propertySelectors';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough';
import { renderWithProviders } from 'test-utils/reactUtils';
import OfferPointsPaySlider from './OfferPointsPaySlider';
import OfferPayableAtProperty from './OfferPayableAtProperty';
import OfferGuestsText from 'components/OfferGuestsText';
import { useBreakpoints } from 'hooks/useBreakpoints';
import CampaignPriceMessage from 'components/CampaignPriceMessage';
import usePointsRedemptionStrikethrough from 'hooks/optimizely/usePointsRedemptionStrikethrough';
import { mocked } from 'test-utils';

jest.mock('./OfferPointsPaySlider', () => ({
  __esModule: true,
  default: jest.fn(({ children, ...props }) => <div data-testid="offer-points-pay-slider">{children}</div>),
}));
jest.mock('store/pointsConversion/pointsConversionSelectors');
jest.mock('store/pointsBurnTiers/pointsBurnSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('store/campaign/campaignSelectors');
jest.mock('hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough');
jest.mock('hooks/optimizely/useAvailableRoomsMessage');
jest.mock('hooks/optimizely/usePointsRedemptionStrikethrough', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('./OfferCheckoutLink', () => ({
  __esModule: true,
  default: jest.fn(({ children, propertyId, roomTypeId, roomName, offerName, offerId, fallback, ...otherProps }) => (
    <div data-testid="select-button-offer-price-box">{children}</div>
  )),
}));

jest.mock('./OfferPayableAtProperty', () => ({
  __esModule: true,
  default: jest.fn(({ children, payableAtProperty, isIncludedInTotal, ...props }) => (
    <div data-testid="offer-payable-at-property">{children}</div>
  )),
}));

jest.mock('components/CampaignPriceMessage', () => ({
  __esModule: true,
  default: jest.fn(({ children, currency, offerType, ...props }) => (
    <div data-testid="campaign-price-message" data-currency={currency} data-offer-type={offerType}>
      {children}
    </div>
  )),
}));

jest.mock('components/PointsRedemptionSash', () => {
  return function MockPointsRedemptionSash({ campaignMessage, type, backgroundColor, color }) {
    return (
      <div data-testid="points-redemption-sash" style={{ backgroundColor, color }}>
        {campaignMessage}
      </div>
    );
  };
});

jest.mock('components/OfferGuestsText', () => ({
  __esModule: true,
  default: jest.fn(({ 'data-testid': dataTestId, children, guests, numberOfNights, ...props }) => (
    <div data-testid={dataTestId}>{children}</div>
  )),
}));

jest.mock('components/PriceStrikethrough', () => ({
  __esModule: true,
  default: ({ children, ...props }) => <div data-testid="price-strikethrough">{children}</div>,
}));

jest.mock('hooks/useBreakpoints');

const mockedUsePointsRedemptionStrikethrough = mocked(usePointsRedemptionStrikethrough);

const defaultProps = {
  onCashPaymentAmountChange: jest.fn(),
  offerType: 'standard',
  checkoutUrl: 'http://checkoutpage.com',
  roomTypeId: '2',
  isLuxuryOffer: false,
  offerId: '3',
  offerName: 'Best Rate',
  roomName: 'Standard King',
  cancellationPolicy: {
    isNonrefundable: true,
    description: 'Non Refundable',
    cancellationWindows: [],
  },
  depositPay: {},
  charges: {
    total: {
      amount: '159.00',
      currency: 'AUD',
    },
    totalBeforeDiscount: {
      amount: '159.00',
      currency: 'AUD',
    },
    totalDiscount: {
      amount: '0',
      currency: 'AUD',
    },
    payableAtBooking: {
      total: {
        amount: '149.00',
        currency: 'AUD',
      },
    },
    payableAtProperty: {
      total: {
        amount: '0',
        currency: 'AUD',
      },
    },
    strikethrough: {
      price: {
        amount: '179000',
        currency: 'AUD',
      },
    },
  },
};

const checkIn = new Date(2025, 3, 30);
const checkOut = new Date(2025, 4, 1);
const propertyId = '1';
const isPointPay = true;
const isNotPointPay = false;

const renderComponent = (props = {}) => renderWithProviders(<OfferPriceBox {...defaultProps} {...props} />);

beforeEach(() => {
  jest.clearAllMocks();
  getLevels.mockReturnValue(TIER_9);
  getPointsLevels.mockReturnValue(TIER_9);
  getQueryCheckIn.mockReturnValue(checkIn);
  getQueryCheckOut.mockReturnValue(checkOut);
  getPropertyId.mockReturnValue(propertyId);
  getIsPointsPay.mockReturnValue(isNotPointPay);
  useBreakpoints.mockReturnValue({ isLessThanBreakpoint: () => false });
  mockedUsePointsRedemptionStrikethrough.mockReturnValue({ isReady: true, isPointsRedemptionStrikethrough: true });

  usePriceStrikethrough.mockReturnValue({
    isReady: true,
    showFullTraffic: false,
    showVariationB: false,
  });
  useAvailableRoomsMessage.mockReturnValue({
    isReady: true,
    showMessage: false,
    max_rooms_cutoff: 5,
  });
});

describe('OfferPriceBox', () => {
  describe('with in cash mode', () => {
    it('shows the OfferGuestsText component', () => {
      renderComponent();
      expect(screen.getByTestId('select-button-offer-price-box')).toBeInTheDocument();
    });

    it('shows the currency after the number of nights', () => {
      renderComponent();
      expect(screen.getByTestId('currency-symbol-not-from-price')).toBeInTheDocument();
    });

    it('Shows the price', () => {
      renderComponent();
      expect(screen.getByTestId('total-to-pay')).toHaveTextContent('$159');
    });

    it('Shows the currency', () => {
      renderComponent();
      expect(screen.getByText('AUD')).toBeInTheDocument();
    });

    it('does not show the * after the currency', () => {
      renderComponent();
      expect(screen.queryByText('*')).not.toBeInTheDocument();
    });

    it('renders the OfferPayableAtProperty with correct props when payableAtProperty total is 0 AUD', () => {
      renderComponent();
      expect(screen.getByTestId('offer-payable-at-property')).toBeInTheDocument();
      expect(OfferPayableAtProperty).toHaveBeenCalledWith(
        expect.objectContaining({
          payableAtProperty: defaultProps.charges.payableAtProperty.total,
          isIncludedInTotal: true,
        }),
        {},
      );
    });

    it('renders the OfferCheckoutLink with the correct props', () => {
      const OfferCheckoutLink = require('./OfferCheckoutLink').default;
      renderComponent();

      expect(OfferCheckoutLink).toHaveBeenCalledWith(
        expect.objectContaining({
          propertyId: propertyId,
          roomTypeId: defaultProps.roomTypeId,
          roomName: defaultProps.roomName,
          offerName: defaultProps.offerName,
          offerId: defaultProps.offerId,
        }),
        {},
      );
    });

    it('renders the CampaignPriceMessage with the correct props', () => {
      renderComponent();

      expect(CampaignPriceMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          currency: 'AUD',
          offerType: 'standard',
        }),
        {},
      );
    });
  });

  describe('when in points mode', () => {
    const props = {
      ...defaultProps,
      charges: {
        total: {
          amount: '2795',
          currency: 'PTS',
        },
        totalCash: {
          amount: '100',
          currency: 'AUD',
        },
        totalBeforeDiscount: {
          amount: '2795',
          currency: 'PTS',
        },
        totalDiscount: {
          amount: 0,
          currency: 'PTS',
        },
        payableAtProperty: {
          total: {
            amount: '0',
            currency: 'AUD',
          },
        },
        payableAtBooking: {
          total: {
            amount: '2795',
            currency: 'PTS',
          },
        },
      },
    };

    it('shows the OfferGuestsText component', () => {
      renderComponent(props);

      expect(screen.getByTestId('select-button-offer-price-box')).toBeInTheDocument();
    });

    it('Shows the price', () => {
      renderComponent(props);
      expect(screen.getByText('2,795')).toBeInTheDocument();
    });

    it('Shows the currency', () => {
      renderComponent(props);
      expect(screen.getByText('PTS')).toBeInTheDocument();
    });

    it('Shows the * after the currency', () => {
      renderComponent(props);
      expect(screen.getByText('*')).toBeInTheDocument();
    });

    it('renders the OfferPayableAtProperty with correct props', () => {
      renderComponent(props);
      expect(screen.getByTestId('offer-payable-at-property')).toBeInTheDocument();
      expect(OfferPayableAtProperty).toHaveBeenCalledWith(
        expect.objectContaining({
          payableAtProperty: props.charges.payableAtProperty.total,
          isIncludedInTotal: false,
        }),
        {},
      );
    });
  });

  describe('when in points + pay mode', () => {
    beforeEach(() => {
      getIsPointsPay.mockReturnValue(isPointPay);
    });

    it('does render the OfferPayableAtProperty', () => {
      renderComponent({ isPointsPay: true });
      expect(screen.getByTestId('offer-payable-at-property')).toBeInTheDocument();
    });

    it('does render the total price with `total-to-pay` test ID', () => {
      renderComponent({ isPointsPay: true });
      expect(screen.getByTestId('total-to-pay')).toBeInTheDocument();
    });

    it('renders the OfferPointsPaySlider when in cash mode', () => {
      renderComponent();
      expect(OfferPointsPaySlider).toHaveBeenCalledWith(
        expect.objectContaining({
          totalCash: defaultProps.charges.total,
          payableAtProperty: defaultProps.charges.payableAtProperty.total,
          propertyId: propertyId,
          roomTypeId: defaultProps.roomTypeId,
          offerId: defaultProps.offerId,
          offerType: defaultProps.offerType,
        }),
        {},
      );
    });

    it('renders the OfferPointsPaySlider when in points mode', () => {
      const charges = {
        total: {
          amount: '27950',
          currency: 'PTS',
        },
        totalCash: {
          amount: '100',
          currency: 'AUD',
        },
        totalBeforeDiscount: {
          amount: '30000',
          currency: 'PTS',
        },
        totalDiscount: {
          amount: '2050',
          currency: 'PTS',
        },
        payableAtProperty: {
          total: {
            amount: '10',
            currency: 'AUD',
          },
        },
        payableAtBooking: {
          total: {
            amount: '27950',
            currency: 'PTS',
          },
        },
      };
      renderComponent({
        ...defaultProps,
        isPointsPay: true,
        charges,
      });

      expect(OfferPointsPaySlider).toHaveBeenCalledWith(
        expect.objectContaining({
          totalCash: charges.totalCash,
          payableAtProperty: charges.payableAtProperty.total,
          propertyId: propertyId,
          roomTypeId: defaultProps.roomTypeId,
          offerId: defaultProps.offerId,
          offerType: defaultProps.offerType,
        }),
        {},
      );
    });

    it('does not show the currency after the number of nights', () => {
      renderComponent({ isPointsPay: true });
      expect(screen.queryByTestId('currency-symbol')).not.toBeInTheDocument();
    });

    it('does not show the slider for classic offers', () => {
      renderComponent({ isPointsPay: true, offerType: 'classic' });
      expect(OfferPointsPaySlider).not.toHaveBeenCalled();
    });

    it('does not show the slider for cash based payableAtBooking amounts less than 5000 points', () => {
      renderComponent({
        isPointsPay: true,
        charges: { ...defaultProps.charges, payableAtBooking: { total: { amount: '25.00', currency: 'AUD' } } },
      });
      expect(OfferPointsPaySlider).not.toHaveBeenCalled();
    });

    it('does not show the slider for points based payableAtBooking amounts less than 5000 points', () => {
      renderComponent({
        isPointsPay: true,
        charges: { ...defaultProps.charges, payableAtBooking: { total: { amount: '4999', currency: 'PTS' } } },
      });
      expect(OfferPointsPaySlider).not.toHaveBeenCalled();
    });
  });

  describe('qantas-hotels-available-rooms-message experiment', () => {
    const propsWithPoints = {
      ...defaultProps,
      charges: {
        total: {
          amount: '2795',
          currency: 'PTS',
        },
        totalCash: {
          amount: '100',
          currency: 'AUD',
        },
        totalBeforeDiscount: {
          amount: '2795',
          currency: 'PTS',
        },
        totalDiscount: {
          amount: '0',
          currency: 'PTS',
        },
        payableAtProperty: {
          total: {
            amount: '0',
            currency: 'AUD',
          },
        },
        payableAtBooking: {
          total: {
            amount: '2795',
            currency: 'PTS',
          },
        },
      },
    };

    it('does not display message when experiment is OFF', () => {
      renderComponent({ ...propsWithPoints, allocationsAvailable: 1 });
      expect(screen.queryByText('Hurry, we only have 1 room left!')).not.toBeInTheDocument();
    });

    describe('when the qantas-hotels-available-rooms-message feature flag is ON', () => {
      beforeEach(() => {
        useAvailableRoomsMessage.mockReturnValue({
          isReady: true,
          showMessage: true,
          max_rooms_cutoff: 5,
        });
      });

      it('does not display message when allocations prop is missing', () => {
        renderComponent({ ...propsWithPoints });
        expect(screen.queryByText('Hurry, we only have 1 room left!')).not.toBeInTheDocument();
      });

      it('does not display message when allocations is larger than max cutoff', () => {
        renderComponent({ ...propsWithPoints, allocationsAvailable: 6 });
        expect(screen.queryByText('We only have 6 rooms left')).not.toBeInTheDocument();
      });

      it('displays message when experiment is ON and allocations are within cutoff', () => {
        renderComponent({ ...propsWithPoints, allocationsAvailable: 1 });
        expect(screen.getByText('Hurry, we only have 1 room left!')).toBeInTheDocument();
      });

      it('displays message for multiple rooms when allocations are within cutoff', () => {
        renderComponent({ ...propsWithPoints, allocationsAvailable: 3 });
        expect(screen.getByText('We only have 3 rooms left')).toBeInTheDocument();
      });
    });
  });

  describe('PriceStrikethrough', () => {
    it('does not render the PriceStrikethrough when the flag variant is not showFullTraffic or showVariationB', () => {
      renderComponent();
      expect(screen.queryByTestId('price-strikethrough')).not.toBeInTheDocument();
    });

    describe('when the qantas-hotels-price-strikethrough flag variant is showFullTraffic or showVariationB', () => {
      beforeEach(() => {
        usePriceStrikethrough.mockReturnValue({
          isReady: true,
          showFullTraffic: true,
          showVariationB: false,
        });
      });

      it('does not render PriceStrikethrough when offer type is classic', () => {
        renderComponent({ offerType: 'classic' });
        expect(screen.queryByTestId('price-strikethrough')).not.toBeInTheDocument();
      });

      it('does not render PriceStrikethrough when price strikethrough is not available (null price)', () => {
        renderComponent({
          charges: { ...defaultProps.charges, strikethrough: { price: null, discount: null, percentage: null } },
        });
        expect(screen.queryByTestId('price-strikethrough')).not.toBeInTheDocument();
      });
    });
    it('does not display price strikethrough when `isPriceStrikethrough` hook returns false', () => {
      usePriceStrikethrough.mockReturnValue({
        isReady: true,
        showFullTraffic: false,
        showVariationB: false,
      });
      renderComponent();
      expect(screen.queryByTestId('price-strikethrough')).not.toBeInTheDocument();
    });
  });

  describe('PriceBeforeDiscount when isPointsRedemptionStrikethrough is true', () => {
    describe('when paywith is points, hasDiscount is true, isLuxuryOffer is false and offer is not classic', () => {
      it('should render the correct price before discount', () => {
        renderComponent({
          charges: {
            ...defaultProps.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
        });
        expect(screen.getByTestId('price-before-discount')).toHaveTextContent('2,500 PTS');
      });
    });

    describe('when paywith is points, isLuxuryOffer is false, offer is not classic but there is no discount', () => {
      it('should not render the price before discount', () => {
        renderComponent({
          charges: {
            ...defaultProps.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2000', currency: 'PTS' },
            totalDiscount: { amount: '0', currency: 'PTS' },
          },
        });
        expect(screen.queryByTestId('price-before-discount')).not.toBeInTheDocument();
      });
    });

    describe('when paywith is points, isLuxuryOffer is false, hasDiscount is true and offer type is classic', () => {
      it('should not render the price before discount', () => {
        renderComponent({
          offerType: 'classic',
          charges: {
            ...defaultProps.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
        });
        expect(screen.queryByTestId('price-before-discount')).not.toBeInTheDocument();
      });
    });

    describe('when paywith is cash, isLuxuryOffer is false, hasDiscount is true and offer type is not classic', () => {
      it('should not render the price before discount', () => {
        renderComponent({
          charges: {
            ...defaultProps.charges,
            total: { amount: '200', currency: 'AUD' },
            totalBeforeDiscount: { amount: '250', currency: 'AUD' },
            totalDiscount: { amount: '50', currency: 'AUD' },
          },
        });
        expect(screen.queryByTestId('price-before-discount')).not.toBeInTheDocument();
      });
    });

    describe('when paywith is points, hasDiscount is true and offer type is classic but isLuxuryOffer is true', () => {
      it('should not render the price before discount', () => {
        renderComponent({
          isLuxuryOffer: true,
          charges: {
            ...defaultProps.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
        });
        expect(screen.queryByTestId('price-before-discount')).not.toBeInTheDocument();
      });
    });

    describe('showPointsRedemptionStrikethrough is true', () => {
      it('should render the PointsRedemptionSash', () => {
        renderComponent({
          charges: {
            ...defaultProps.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
        });

        expect(screen.getByTestId('points-redemption-sash')).toBeInTheDocument();
      });
    });
  });

  describe('PriceBeforeDiscount when isPointsRedemptionStrikethrough is false', () => {
    describe('when paywith is points, isLuxuryOffer is false, hasDiscount is true and offer is not classic', () => {
      beforeEach(() => {
        mockedUsePointsRedemptionStrikethrough.mockReturnValue({ isReady: true, isPointsRedemptionStrikethrough: false });
      });

      it('should not render the price before discount', () => {
        renderComponent({
          charges: {
            ...defaultProps.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
        });
        expect(screen.queryByTestId('price-before-discount')).not.toBeInTheDocument();
      });
    });

    it('renders the CampaignPriceMessage with the correct props', () => {
      mockedUsePointsRedemptionStrikethrough.mockReturnValue({ isReady: true, isPointsRedemptionStrikethrough: false });
      renderComponent({
        charges: {
          ...defaultProps.charges,
          total: { amount: '2000', currency: 'PTS' },
          totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
          totalDiscount: { amount: '500', currency: 'PTS' },
        },
      });

      expect(screen.getByTestId('campaign-price-message')).toBeInTheDocument();
      expect(screen.getByTestId('campaign-price-message')).toHaveAttribute('data-currency', 'PTS');
      expect(screen.getByTestId('campaign-price-message')).toHaveAttribute('data-offer-type', 'standard');
    });
  });

  describe('Accessibility and User Interaction', () => {
    it('renders a link for checkout', () => {
      useBreakpoints.mockReturnValue({ isLessThanBreakpoint: () => false });
      renderComponent();
      expect(screen.getByTestId('select-button-offer-price-box')).toBeInTheDocument();
    });

    it('does not render checkout link when on mobile and slider is showing', () => {
      useBreakpoints.mockReturnValue({ isLessThanBreakpoint: jest.fn(() => true) });
      getIsPointsPay.mockReturnValue(true);
      renderComponent();
      expect(screen.queryByTestId('select-button-offer-price-box')).not.toBeInTheDocument();
    });

    it('calls onCashPaymentAmountChange when relevant interaction occurs (if applicable)', async () => {
      const OfferPointsPaySlider = require('./OfferPointsPaySlider').default;
      const handleCashPaymentChange = jest.fn();
      getIsPointsPay.mockReturnValue(true);
      renderComponent({ onCashPaymentAmountChange: handleCashPaymentChange });

      expect(OfferPointsPaySlider).toHaveBeenCalledWith(
        expect.objectContaining({ onCashPaymentAmountChange: handleCashPaymentChange }),
        {},
      );
    });
  });

  describe('Conditional Rendering - Cancellation Policy', () => {
    it('displays "Non Refundable" text when cancellationPolicy is non-refundable', () => {
      renderComponent({
        cancellationPolicy: {
          isNonrefundable: true,
          description: 'Non Refundable',
          cancellationWindows: [],
        },
      });
      expect(screen.getByRole('button')).toHaveTextContent(/Non-refundable/i);
    });

    it('displays "Free Cancellation" text when cancellationPolicy is refundable', () => {
      renderComponent({
        cancellationPolicy: {
          isNonrefundable: false,
          description: 'Free Cancellation',
          cancellationWindows: [],
        },
      });
      expect(screen.getByText(/Free Cancellation/i)).toBeInTheDocument();
    });
  });

  it('displays the correct number of guests and nights in OfferGuestsText', () => {
    getQueryAdults.mockReturnValue(1);
    getQueryChildren.mockReturnValue(2);
    getQueryInfants.mockReturnValue(1);
    getQueryCheckIn.mockReturnValue(new Date(2025, 0, 1));
    getQueryCheckOut.mockReturnValue(new Date(2025, 0, 5));
    renderComponent();

    expect(OfferGuestsText).toHaveBeenCalledWith(
      expect.objectContaining({
        guests: 4,
        numberOfNights: 4,
      }),
      {},
    );
  });
});
