/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import CollapsedOffer from './CollapsedOffer';
import { mocked } from 'test-utils';
import { getQueryCheckIn, getQueryCheckOut } from 'store/router/routerSelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import { VALUE_ADD_SASH } from 'config/constants';
import { renderWithProviders } from 'test-utils/reactUtils';
import { useShowRedSashToggle } from 'hooks/useShowRedSashToggle';

jest.mock('hooks/useDataLayer');
jest.mock('store/router/routerSelectors');
jest.mock('hooks/useBreakpoints');
jest.mock('hooks/useShowRedSashToggle');

jest.mock('./components/OfferDescription', () => ({
  __esModule: true,
  default: (props) => {
    const { offerType, inclusions, isExpanded, offerName, valueAdds, onExpandOffer, ...domProps } = props;
    return <div data-testid="offer-description" {...domProps} />;
  },
}));

jest.mock('./components/OfferPricing', () => ({
  __esModule: true,
  default: () => <div data-testid="offer-pricing" />,
}));

jest.mock('components/PromotionalSashNew', () => ({
  __esModule: true,
  default: ({ children, promotionName, ...props }) => (
    <div data-testid="promotional-sash" data-promotion-name={promotionName} {...props}>
      {children}
    </div>
  ),
}));

jest.mock('components/PromotionalSashRedVariant', () => ({
  __esModule: true,
  default: ({ children, promotionName, ...props }) => (
    <div data-testid="promotional-sash-red-variant" data-promotion-name={promotionName} {...props}>
      {children}
    </div>
  ),
}));

const offer = {
  type: 'standard',
  description: 'Junior Suite',
  cancellationPolicy: {
    isNonrefundable: true,
    description: 'Non-refundable unless you are entitled to a refund or other remedy under the Australian Consumer Law.',
    cancellationWindows: [],
  },
  name: 'Breakfast Included - Non-refundable',
  charges: {
    total: {
      amount: '297.99',
      currency: 'AUD',
    },
    totalBeforeDiscount: {
      amount: '297.99',
      currency: 'AUD',
    },
    totalDiscount: {
      amount: 0,
      currency: 'AUD',
    },
    payableAtProperty: {
      total: {
        amount: '0',
        currency: 'AUD',
      },
    },
    payableAtBooking: {
      total: {
        amount: '0',
        currency: 'AUD',
      },
    },
    totalCash: { amount: '297.99', currency: 'AUD' },
    strikethrough: {
      price: {
        amount: '179000',
        currency: 'AUD',
      },
      discount: {
        amount: null,
        currency: null,
      },
      percentage: null,
    },
  },
  inclusions: [
    {
      code: 'breakfast',
      name: 'Breakfast Included',
      description: 'Breakfast Buffet',
      icon: 'incQfBreakfast',
    },
  ],
  valueAdds: [],
  id: '202017398_230005304_493003',
  promotion: {},
  depositPay: {
    depositPayable: true,
  },
  pointsEarned: {
    maxQffEarnPpd: 1,
    maxQbrEarnPpd: 1,
    qbrPoints: { total: 1 },
    qffPoints: { qffPointsClub: 10, total: 10, bonus: 0, base: 0 },
    promotionMultiplier: 1,
    propertyPpd: 1,
  },
  pointsTierInstanceId: 'test-tier-instance-id',
};

const promotion = { name: 'a great deal' };
const valueAdds = ['Free bottle of wine'];
const checkIn = new Date(2019, 3, 30);
const checkOut = new Date(2019, 4, 1);
const toggleOfferExpanded = jest.fn();
const emitInteractionEvent = jest.fn();

const defaultProps = {
  offer: offer,
  toggleOfferExpanded,
};

const renderComponent = (props = {}) => renderWithProviders(<CollapsedOffer {...defaultProps} {...props} />);

beforeEach(() => {
  jest.resetAllMocks();
  mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  mocked(getQueryCheckIn).mockReturnValue(checkIn);
  mocked(getQueryCheckOut).mockReturnValue(checkOut);
  mocked(useShowRedSashToggle).mockReturnValue({ showRedSash: false, isReady: true });
});

describe('CollapsedOffer', () => {
  it('fires the toggleOfferExpanded prop when the "Expand offer summary" button is clicked', async () => {
    renderComponent();
    await userEvent.click(screen.getByRole('button', { name: /Expand offer details/i }));
    expect(toggleOfferExpanded).toHaveBeenCalledWith(true);
  });

  describe('PromotionalSash', () => {
    it('does NOT render when there is NO promotion and NO ValueAdds', () => {
      renderComponent({
        offer: {
          ...offer,
          valueAdds: [],
          promotion: {},
        },
      });
      expect(screen.queryByTestId('promotional-sash')).not.toBeInTheDocument();
    });

    describe('when there is a promotion', () => {
      it('sends the promotion name when ValueAdds are present', () => {
        renderComponent({
          offer: {
            ...offer,
            valueAdds,
            promotion,
          },
        });

        expect(screen.getByTestId('promotional-sash')).toBeInTheDocument();
        expect(screen.getByTestId('promotional-sash')).toHaveAttribute('data-promotion-name', promotion.name);
      });

      it('sends the promotion name when NO ValueAdds are present', () => {
        renderComponent({ offer: { ...offer, promotion: { name: promotion.name } } });

        expect(screen.getByTestId('promotional-sash')).toBeInTheDocument();
        expect(screen.getByTestId('promotional-sash')).toHaveAttribute('data-promotion-name', promotion.name);
      });
    });

    describe('when there is NO promotion', () => {
      it('sends VALUE_ADD_SASH as promotion name when ValueAdds are present', () => {
        renderComponent({ offer: { ...offer, valueAdds } });

        expect(screen.getByTestId('promotional-sash')).toBeInTheDocument();
        expect(screen.getByTestId('promotional-sash')).toHaveAttribute('data-promotion-name', VALUE_ADD_SASH);
      });
    });
    describe('when showRedSash is true', () => {
      it('renders the red sash', () => {
        mocked(useShowRedSashToggle).mockReturnValue({ showRedSash: true, isReady: true });
        renderComponent({ offer: { ...offer, valueAdds } });

        expect(screen.getByTestId('promotional-sash-red-variant')).toBeInTheDocument();
      });
    });
  });

  it('renders the "Expand offer summary" button', () => {
    renderComponent();
    expect(screen.getByRole('button', { name: /expand offer details/i })).toBeInTheDocument();
  });

  it('sends an event to the dataLayer when the "Expand offer summary" button is clicked', async () => {
    renderComponent();
    await userEvent.click(screen.getByRole('button', { name: /expand offer details/i }));
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Room Offer Details',
      value: 'Offer Expanded',
    });
  });
});
