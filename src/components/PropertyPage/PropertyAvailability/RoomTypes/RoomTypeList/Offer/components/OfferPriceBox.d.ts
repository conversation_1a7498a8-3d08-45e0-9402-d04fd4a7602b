// TODO: Delete this file once OfferPriceBox is converted to TypeScript.
// Temporary type definitions for OfferPriceBox.
import * as React from 'react';
import { Decimal } from 'decimal.js';
import { CancellationPolicy, DepositPay, Charges } from '../../../../../../../types/property';

export interface OfferPriceBoxProps {
  allocationsAvailable: number | undefined;
  charges: Charges;
  offerType: string;
  offerName: string;
  offerId: string;
  roomTypeId: string | number;
  roomName: string;
  onCashPaymentAmountChange: (args: { cashAmount: Decimal }) => void;
  offerInstanceId: string;
  cancellationPolicy: CancellationPolicy;
  depositPay: DepositPay;
  isLuxuryOffer?: boolean;
}

declare const OfferPriceBox: React.FC<OfferPriceBoxProps>;
export default OfferPriceBox;
