import React, { Fragment, memo } from 'react';
import PropTypes from 'prop-types';
import get from 'lodash/get';
import styled from '@emotion/styled';
import { Decimal } from 'decimal.js';
import { themeGet } from 'styled-system';
import { useSelector } from 'react-redux';
import { Box, Flex, Icon, Text } from '@qga/roo-ui/components';
import { differenceInCalendarDays } from 'lib/date';
import { MIN_POINTS_AMOUNT } from 'config';
import usePointsConverters from 'hooks/points/usePointsConverters';
import { getQueryCheckIn, getQueryCheckOut, getQueryAdults, getQueryChildren, getQueryInfants } from 'store/router/routerSelectors';
import { getPropertyId } from 'store/property/propertySelectors';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import Currency from 'components/Currency';
import CampaignPriceMessage from 'components/CampaignPriceMessage';
import OfferGuestsText from 'components/OfferGuestsText';
import OfferPointsPaySlider from './OfferPointsPaySlider';
import OfferCheckoutLink from './OfferCheckoutLink';
import OfferPayableAtProperty from './OfferPayableAtProperty';
import OfferDepositPayMessage from './OfferDepositPayMessage';
import { useBreakpoints } from 'hooks/useBreakpoints';
import CancellationTooltip from 'components/CancellationTooltip/CancellationTooltip';
import CancellationRefundModal from 'components/CancellationRefundModal';
import RoomsAvailabilityMessage from 'components/RoomsAvailabilityMessage';
import PriceStrikethrough from 'components/PriceStrikethrough';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough';
import PriceBeforeDiscount from 'components/PriceBeforeDiscount';
import usePointsRedemptionStrikethrough from 'hooks/optimizely/usePointsRedemptionStrikethrough';
import PointsRedemptionSash from 'components/PointsRedemptionSash';
import { FormatNumber } from 'components/formatters';

const canPayWithPoints = ({ payableAtBooking, convertCashToPoints }) => {
  const payableAtBookingInPoints =
    payableAtBooking.total.currency === 'PTS'
      ? new Decimal(payableAtBooking.total.amount)
      : convertCashToPoints({ cash: payableAtBooking.total.amount });

  return payableAtBookingInPoints.greaterThanOrEqualTo(MIN_POINTS_AMOUNT);
};

const BoxWithLeft = styled.div`
  border-left: 3px solid ${themeGet('colors.brand.primary')};
  padding-left: 8px;
  margin-bottom: 12px;
}
`;

const OfferPriceBox = memo(
  ({
    allocationsAvailable,
    charges,
    offerType,
    offerName,
    offerId,
    roomTypeId,
    roomName,
    onCashPaymentAmountChange,
    offerInstanceId,
    depositPay,
    cancellationPolicy,
    isLuxuryOffer,
  }) => {
    const propertyId = useSelector(getPropertyId);
    const isPointsPay = useSelector(getIsPointsPay);
    const isDepositPay = depositPay?.depositPayable;

    const checkIn = useSelector(getQueryCheckIn);
    const checkOut = useSelector(getQueryCheckOut);
    const totalNumberOfNights = differenceInCalendarDays(checkOut, checkIn);

    const adults = useSelector(getQueryAdults) || 0;
    const children = useSelector(getQueryChildren) || 0;
    const infants = useSelector(getQueryInfants) || 0;
    const totalGuests = adults + children + infants;

    const { convertCashToPoints } = usePointsConverters();
    const { total, payableAtProperty, payableAtBooking, totalCash, totalBeforeDiscount, totalDiscount } = charges;
    const currency = get(charges, 'total.currency');
    const hasDiscount = totalDiscount?.amount > 0;
    const isCurrencyCash = currency !== 'PTS';
    const isClassic = offerType === 'classic';

    const isShowingPointsPaySlider = isPointsPay && !isClassic && canPayWithPoints({ isClassic, payableAtBooking, convertCashToPoints });
    const isIncludedInTotal = isCurrencyCash;
    const { isPointsRedemptionStrikethrough } = usePointsRedemptionStrikethrough();

    const { isLessThanBreakpoint } = useBreakpoints();
    const isMobile = isLessThanBreakpoint(0);

    const { showMessage, max_rooms_cutoff } = useAvailableRoomsMessage();
    const availableRoomsMaxCutoff = max_rooms_cutoff ?? 5;
    const showAvailableRooms =
      showMessage && allocationsAvailable && allocationsAvailable > 0 && allocationsAvailable <= availableRoomsMaxCutoff;

    const { showFullTraffic, showVariationB } = usePriceStrikethrough();
    const isPriceStrikethrough = showFullTraffic || showVariationB;
    const showPointsRedemptionStrikethrough =
      !isCurrencyCash && !isLuxuryOffer && hasDiscount && !isClassic && isPointsRedemptionStrikethrough;
    const pointsValue = FormatNumber({ number: charges.totalDiscount?.amount ?? 0, decimalPlaces: 0 });
    const strikethroughPrice = charges?.strikethrough?.price;
    const isPriceStrikeAvailable = !!strikethroughPrice?.amount;

    const showPriceStrikethrough = isPriceStrikeAvailable && isPriceStrikethrough && !isClassic;

    return (
      <Box data-testid="offer-price-box" pt={isShowingPointsPaySlider ? 0 : [4, 0]} width="100%">
        <Fragment>
          <BoxWithLeft>
            <OfferGuestsText guests={totalGuests} numberOfNights={totalNumberOfNights} color={'greys.steel'} fontSize="sm" />
            <Flex flexDirection="row" justifyContent="flex-start" alignItems="baseline" flexWrap="wrap">
              <Flex alignItems="center" justifyContent={['flex-start', 'flex-end']}>
                {isClassic && <Icon name="ribbon" mr="2" color="greys.charcoal" />}
                <Currency
                  amount={total.amount}
                  currency={total.currency}
                  roundToCeiling
                  fontSize={32}
                  hideCurrency={true}
                  fontWeight="bold"
                  color="greys.charcoal"
                  data-testid="total-to-pay"
                  alignCurrency="superscript"
                />
                {!(showPriceStrikethrough && isCurrencyCash) && (
                  <Text pl={1} pr={2} pb="10px" fontWeight={'bold'} data-testid="luxe-currency">
                    {currency}
                    {!isCurrencyCash && <sup>*</sup>}
                  </Text>
                )}
                {showPriceStrikethrough && (
                  <Box pb={2}>
                    <PriceStrikethrough price={strikethroughPrice} ml={2} mt={isCurrencyCash ? 3 : 2} />
                  </Box>
                )}
                {showPointsRedemptionStrikethrough && (
                  <PriceBeforeDiscount
                    total={totalBeforeDiscount}
                    discount={totalDiscount}
                    hideCurrency={isCurrencyCash}
                    fontSize={['xs', 'sm']}
                    lineHeight="0.6"
                    roundToCeiling
                    offerType={offerType}
                    showMessage={false}
                    pb="10px"
                  />
                )}
              </Flex>
            </Flex>
            {showPointsRedemptionStrikethrough ? (
              <Box mt={1}>
                <PointsRedemptionSash campaignMessage={`Save ${pointsValue} PTS`} backgroundColor="green" color="white" />
              </Box>
            ) : (
              <CampaignPriceMessage
                currency={total.currency}
                offerType={offerType}
                fallback
                color={showAvailableRooms ? 'greys.steel' : undefined}
              />
            )}
          </BoxWithLeft>
          <Box mb={3}>
            {showAvailableRooms && <RoomsAvailabilityMessage isOffer={true} allocationsAvailable={allocationsAvailable} />}
            {isMobile ? (
              <Box my={2}>
                <CancellationRefundModal cancellationPolicy={cancellationPolicy} fontSize={['xs', 'sm']} mb={-2} />
              </Box>
            ) : (
              <Box data-testid="cancellation-tool-tip-desktop">
                <CancellationTooltip cancellationPolicy={cancellationPolicy} fontSize={['xs', 'sm']} />
              </Box>
            )}
            {isDepositPay && <OfferDepositPayMessage depositPay={depositPay} />}
          </Box>
          <OfferPayableAtProperty payableAtProperty={payableAtProperty.total} isIncludedInTotal={isIncludedInTotal} />
          {isShowingPointsPaySlider && (
            <Fragment>
              <OfferPointsPaySlider
                hasAvailability={true}
                offerName={offerName}
                offerType={offerType}
                totalCash={totalCash || total} //totalCash is a temporary attribute passed by Ava in points mode due to the total in points not being the full total (payableAtBooking + payableAtProperty)
                totalPointsAmount={total.amount}
                payableAtProperty={payableAtProperty.total}
                offerId={offerId}
                offerInstanceId={offerInstanceId}
                roomTypeId={roomTypeId}
                propertyId={propertyId}
                onCashPaymentAmountChange={onCashPaymentAmountChange}
                roomName={roomName}
                campaignPriceMessage={<CampaignPriceMessage currency={total.currency} offerType={offerType} />}
                depositPay={depositPay}
                isMobile={isMobile}
                cancellationPolicy={cancellationPolicy}
                isClassic={isClassic}
              />
            </Fragment>
          )}

          {!isShowingPointsPaySlider && !isMobile && (
            <OfferCheckoutLink
              propertyId={propertyId}
              roomTypeId={roomTypeId}
              offerId={offerId}
              offerName={offerName}
              roomName={roomName}
              data-testid="select-button-offer-price-box"
            />
          )}
        </Fragment>
      </Box>
    );
  },
);

OfferPriceBox.propTypes = {
  allocationsAvailable: PropTypes.number,
  charges: PropTypes.object.isRequired,
  offerType: PropTypes.string.isRequired,
  cancellationPolicy: PropTypes.shape({
    isNonrefundable: PropTypes.bool.isRequired,
    description: PropTypes.string.isRequired,
  }).isRequired,
  depositPay: PropTypes.object.isRequired,
  roomTypeId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  offerId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  offerName: PropTypes.string.isRequired,
  offerInstanceId: PropTypes.string,
  roomName: PropTypes.string.isRequired,
  onCashPaymentAmountChange: PropTypes.func.isRequired,
  isLuxuryOffer: PropTypes.bool,
};

OfferPriceBox.displayName = 'OfferPriceBox';

export default OfferPriceBox;
