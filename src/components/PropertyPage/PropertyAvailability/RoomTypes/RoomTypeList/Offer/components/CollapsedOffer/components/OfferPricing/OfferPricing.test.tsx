import React from 'react';
import { screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { OfferPricing } from './OfferPricing';
import { mocked } from 'test-utils';
import { useBreakpoints } from 'hooks/useBreakpoints';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough';
import { renderWithProviders } from 'test-utils/reactUtils';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';
import usePointsRedemptionStrikethrough from 'hooks/optimizely/usePointsRedemptionStrikethrough';

jest.mock('hooks/useBreakpoints');
jest.mock('hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough');
jest.mock('hooks/optimizely/useAvailableRoomsMessage');

jest.mock('hooks/optimizely/usePointsRedemptionStrikethrough', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('components/CampaignPriceMessage', () => ({ children, fallback, offerType, currency, ...props }) => {
  return (
    <div
      data-testid="campaign-price-message"
      data-fallback={fallback ? 'true' : 'false'}
      data-currency={currency}
      data-offer-type={offerType}
      {...props}
    >
      {children}
    </div>
  );
});

jest.mock('components/PointsRedemptionSash', () => {
  return function MockPointsRedemptionSash({ campaignMessage, backgroundColor, color }) {
    return (
      <div data-testid="points-redemption-sash" style={{ backgroundColor, color }}>
        {campaignMessage}
      </div>
    );
  };
});

jest.mock('components/CancellationRefundModal', () => ({
  __esModule: true,
  default: () => <div data-testid="cancellation-refund-modal" />,
}));
jest.mock('components/CancellationTooltip/CancellationTooltip', () => ({
  __esModule: true,
  default: () => <div data-testid="cancellation-tooltip" />,
}));
jest.mock('components/NightsAndGuests', () => ({
  __esModule: true,
  default: () => <div data-testid="nights-and-guests" />,
}));
jest.mock('components/PriceBeforeDiscount', () => {
  return function MockPriceBeforeDiscount({ total, discount }) {
    const amount = total?.amount ?? discount?.amount ?? '';
    return (
      <div data-testid="PriceBeforeDiscount" data-amount={amount}>
        {amount}
      </div>
    );
  };
});

const mockedUsePointsRedemptionStrikethrough = mocked(usePointsRedemptionStrikethrough);

const defaultProps = {
  isClassic: false,
  charges: {
    total: {
      amount: '297.99',
      currency: 'AUD',
    },
    totalBeforeDiscount: {
      amount: '297.99',
      currency: 'AUD',
    },
    totalDiscount: {
      amount: 0,
      currency: 'AUD',
    },
    payableAtProperty: {
      total: {
        amount: '0',
        currency: 'AUD',
      },
    },
    payableAtBooking: {
      total: {
        amount: '0',
        currency: 'AUD',
      },
    },
    totalCash: { amount: '297.99', currency: 'AUD' },
    strikethrough: {
      price: {
        amount: '179000',
        currency: 'AUD',
      },
      discount: {
        amount: null,
        currency: null,
      },
      percentage: null,
    },
  },
  allocationsAvailable: 5,
  cancellationPolicy: {
    isNonrefundable: true,
    description: 'Non-refundable unless you are entitled to a refund or other remedy under the Australian Consumer Law.',
    cancellationWindows: [],
  },
  depositPay: {
    depositPayable: true,
    amount: {
      currency: 'AUD',
      value: 100,
    },
  },
  offerType: 'Standard',
  luxOffer: false,
};

const renderComponent = (props = {}) => renderWithProviders(<OfferPricing {...defaultProps} {...props} />);

beforeEach(() => {
  jest.resetAllMocks();
  mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => false });
  mocked(usePriceStrikethrough).mockReturnValue({
    isReady: true,
    showFullTraffic: false,
    showVariationB: false,
  });
  mocked(useAvailableRoomsMessage).mockReturnValue({
    isReady: true,
    showMessage: false,
    max_rooms_cutoff: 5,
  });
  mockedUsePointsRedemptionStrikethrough.mockReturnValue({ isReady: true, isPointsRedemptionStrikethrough: true });
});

describe('OfferPricing', () => {
  it('renders the NightsAndGuests', () => {
    renderComponent();
    expect(screen.getByTestId('nights-and-guests')).toBeInTheDocument();
  });

  it('renders currency with correct amount', () => {
    renderComponent();
    const currency = screen.getByTestId('total-to-pay-new');
    expect(currency).toHaveTextContent('$298');
  });

  it('does not render an asterisk after the currency in cash mode', () => {
    renderComponent();
    expect(screen.queryByText('*')).not.toBeInTheDocument();
  });

  it('renders the CampaignPriceMessage', () => {
    renderComponent();
    expect(screen.getByTestId('campaign-price-message')).toBeInTheDocument();
  });

  describe('PriceStrikethrough', () => {
    it('does not render the PriceStrikethrough when the qantas-hotels-price-strikethrough flag variant is not showFullTraffic or showVariationB', () => {
      renderComponent();
      expect(screen.queryByTestId('price-strikethrough')).not.toBeInTheDocument();
    });

    describe('when the qantas-hotels-price-strikethrough flag variant is showFullTraffic or showVariationB', () => {
      beforeEach(() => {
        mocked(usePriceStrikethrough).mockReturnValue({
          isReady: true,
          showFullTraffic: false,
          showVariationB: true,
        });
      });

      it('renders PriceStrikethrough with correct amount', () => {
        renderComponent();
        expect(screen.getByTestId('price-strikethrough')).toBeInTheDocument();
        expect(screen.getByTestId('price-strikethrough')).toHaveTextContent('$179,000');
      });

      it('does not render PriceStrikethrough when offer type is classic reward', () => {
        renderComponent({
          isClassic: true,
        });
        expect(screen.queryByTestId('price-strikethrough')).not.toBeInTheDocument();
      });

      it('does not render PriceStrikethrough when price strikethrough is not available', () => {
        renderComponent({
          charges: {
            ...defaultProps.charges,
            strikethrough: undefined,
          },
        });
        expect(screen.queryByTestId('price-strikethrough')).not.toBeInTheDocument();
      });
    });
  });

  describe('PriceBeforeDiscount when isPointsRedemptionStrikethrough is true', () => {
    describe('when paywith is points, isLuxuryOffer is false, hasDiscount is true and offer is not classic', () => {
      it('should render the correct price before discount', () => {
        renderComponent({
          charges: {
            ...defaultProps.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
        });
        expect(screen.getByTestId('PriceBeforeDiscount')).toHaveTextContent('2500');
      });
    });

    describe('when paywith is points, isLuxuryOffer is false, offer is not classic but there is no discount', () => {
      it('should not render the price before discount', () => {
        renderComponent({
          charges: {
            ...defaultProps.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2000', currency: 'PTS' },
            totalDiscount: { amount: '0', currency: 'PTS' },
          },
        });
        expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
      });
    });

    describe('when paywith is points, isLuxuryOffer is false, hasDiscount is true and offer type is classic', () => {
      it('should not render the price before discount', () => {
        renderComponent({
          isClassic: true,
          charges: {
            ...defaultProps.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
        });
        expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
      });
    });

    describe('when paywith is cash, isLuxuryOffer is false, hasDiscount is true and offer type is not classic', () => {
      it('should not render the price before discount', () => {
        renderComponent({
          charges: {
            ...defaultProps.charges,
            total: { amount: '200', currency: 'AUD' },
            totalBeforeDiscount: { amount: '250', currency: 'AUD' },
            totalDiscount: { amount: '50', currency: 'AUD' },
          },
        });
        expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
      });
    });

    describe('when paywith is points, hasDiscount is true and offer type is classic but isLuxuryOffer is true ', () => {
      it('should not render the price before discount', () => {
        renderComponent({
          isLuxuryOffer: true,
          charges: {
            ...defaultProps.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
        });
        expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
      });
    });

    describe('showPointsRedemptionStrikethrough is true', () => {
      it('should render the PointsRedemptionSash', () => {
        renderComponent({
          charges: {
            ...defaultProps.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
        });

        expect(screen.getByTestId('points-redemption-sash')).toBeInTheDocument();
      });
    });
  });

  describe('PriceBeforeDiscount when isPointsRedemptionStrikethrough is false', () => {
    describe('when paywith is points, hasDiscount is true, isLuxuryOffer is false and offer is not classic', () => {
      beforeEach(() => {
        mockedUsePointsRedemptionStrikethrough.mockReturnValue({ isReady: true, isPointsRedemptionStrikethrough: false });
      });
      it('should not render the price before discount', () => {
        renderComponent({
          charges: {
            ...defaultProps.charges,
            total: { amount: '2000', currency: 'PTS' },
            totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
            totalDiscount: { amount: '500', currency: 'PTS' },
          },
        });
        expect(screen.queryByTestId('PriceBeforeDiscount')).not.toBeInTheDocument();
      });
    });

    it('renders the CampaignPriceMessage with the correct props', () => {
      mockedUsePointsRedemptionStrikethrough.mockReturnValue({ isReady: true, isPointsRedemptionStrikethrough: false });
      renderComponent({
        charges: {
          ...defaultProps.charges,
          total: { amount: '2000', currency: 'PTS' },
          totalBeforeDiscount: { amount: '2500', currency: 'PTS' },
          totalDiscount: { amount: '500', currency: 'PTS' },
        },
      });

      expect(screen.getByTestId('campaign-price-message')).toBeInTheDocument();
      expect(screen.getByTestId('campaign-price-message')).toHaveAttribute('data-currency', 'PTS');
      expect(screen.getByTestId('campaign-price-message')).toHaveAttribute('data-offer-type', 'Standard');
    });
  });

  describe('refund policy', () => {
    it('when in desktop mode renders the CancellationTooltip', () => {
      renderComponent();
      expect(screen.getByTestId('cancellation-tooltip')).toBeInTheDocument();
      expect(screen.queryByTestId('cancellation-refund-modal')).not.toBeInTheDocument();
    });

    it('when in mobile mode renders the CancellationRefundModal', () => {
      mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => true });
      renderComponent();
      expect(screen.getByTestId('cancellation-refund-modal')).toBeInTheDocument();
      expect(screen.queryByTestId('cancellation-tooltip')).not.toBeInTheDocument();
    });
  });

  describe('Available rooms message', () => {
    it('does not display message when experiment is OFF', () => {
      renderComponent();
      expect(screen.queryByText('Hurry, we only have 1 room left!')).not.toBeInTheDocument();
    });

    it('does not display message when allocations prop is missing', () => {
      renderComponent({ allocationsAvailable: undefined });
      expect(screen.queryByText('Hurry, we only have 1 room left!')).not.toBeInTheDocument();
    });

    describe('when the Feature Flag is ON', () => {
      beforeEach(() => {
        mocked(useAvailableRoomsMessage).mockReturnValue({
          isReady: true,
          showMessage: true,
          max_rooms_cutoff: 5,
        });
      });

      it('does not display message when allocations is larger than max', () => {
        renderComponent({ allocationsAvailable: 6 });
        expect(screen.queryByText('We only have 6 rooms left')).not.toBeInTheDocument();
      });

      it('displays message when allocations is between 2 and max cutoff', () => {
        renderComponent({ allocationsAvailable: 3 });
        expect(screen.getByText('We only have 3 rooms left')).toBeInTheDocument();
      });

      it('displays urgent message when allocations is 1', () => {
        renderComponent({ allocationsAvailable: 1 });
        expect(screen.getByText('Hurry, we only have 1 room left!')).toBeInTheDocument();
      });
    });
  });
});
