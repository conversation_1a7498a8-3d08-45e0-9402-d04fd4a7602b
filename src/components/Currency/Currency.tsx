import React from 'react';
import { Decimal } from 'decimal.js';
import { Box, Flex, Text } from '@qga/roo-ui/components';
import { rem } from 'polished';
import { FormatNumber } from 'components/formatters';
import { Wrapper, Amount, CurrencyText } from './Currency.styles';

const CURRENCY_SYMBOLS = {
  AUD: '$',
};

const resolveCurrencySymbol = (currency) => CURRENCY_SYMBOLS[currency];

interface Props {
  amount?: string | number;
  currency?: string;
  currencyTextFontSize?: string | number | string[] | number[];
  currencyTextSuffix?: string;
  roundToCeiling?: boolean;
  fontSize?: string | number | string[] | number[];
  hideCurrency?: boolean;
  lineHeight?: string | number;
  textDecoration?: string;
  fontWeight?: number | number[] | 'bold' | 'normal';
  alignCurrency?: 'left' | 'right' | 'superscript';
  alignItems?: string;
  color?: string;
  termsConditionsSymbol?: string;
  currencySymbolProps?: Record<string, unknown>;
  fromPrice?: boolean;
  exclusiveOffer?: boolean;
  testId?: string;
  isMobile?: boolean;
  showCurrencySymbol?: boolean;
}

const Currency = ({
  amount,
  currency,
  currencyTextFontSize = 'xs',
  currencyTextSuffix = '',
  roundToCeiling = false,
  fontSize = ['sm', 'base'],
  hideCurrency = false,
  alignCurrency = 'right',
  lineHeight = 'tight',
  fontWeight = 'normal',
  textDecoration = 'none',
  alignItems = 'baseline',
  color,
  termsConditionsSymbol,
  currencySymbolProps = {},
  fromPrice = false,
  testId = 'currency',
  showCurrencySymbol = true,
  ...rest
}: Props) => {
  if (amount === null || amount === undefined) return null;

  const amountAsFloat = typeof amount === 'string' ? parseFloat(amount.replace(/[^0-9.]/g, '')) : amount;
  const currencySymbol = resolveCurrencySymbol(currency);
  const number = roundToCeiling ? new Decimal(amountAsFloat).ceil() : new Decimal(amountAsFloat);
  const decimalPlaces = currency === 'PTS' || roundToCeiling ? 0 : 2;
  const isPoints = currency === 'PTS';
  const reverse = alignCurrency === 'left';

  return (
    <Wrapper
      data-testid={testId}
      lineHeight={lineHeight}
      fontWeight={fontWeight}
      isPoints={isPoints}
      reverse={reverse}
      alignItems={alignItems}
      color={color}
      {...rest}
    >
      {fromPrice && (
        <Flex flexDirection="row">
          {currencySymbol && showCurrencySymbol && (
            <Text data-testid="currency-symbol" textStyle="currency" fontSize="20px" fontWeight={fontWeight} {...currencySymbolProps}>
              <Text>{currencySymbol}</Text>
            </Text>
          )}
          <Amount textStyle="currency" fontSize={32} lineHeight={lineHeight} textDecoration={textDecoration} data-testid="amount">
            {FormatNumber({ number, decimalPlaces })}
          </Amount>
          {termsConditionsSymbol && (
            <Text data-testid="terms-conditions-symbol" fontSize={fontSize} lineHeight={lineHeight} fontWeight={fontWeight}>
              {alignCurrency === 'superscript' ? (
                <sup style={{ fontSize: rem('20px') }}>{termsConditionsSymbol}</sup>
              ) : (
                termsConditionsSymbol
              )}
            </Text>
          )}
        </Flex>
      )}

      {!fromPrice && (
        <Flex fontSize={fontSize} alignItems={'center'}>
          {currencySymbol && showCurrencySymbol && (
            <Text
              data-testid="currency-symbol-not-from-price"
              textStyle="currency"
              fontSize={fontSize}
              lineHeight={lineHeight}
              fontWeight={fontWeight}
              {...currencySymbolProps}
            >
              {alignCurrency === 'superscript' ? (
                <sup style={{ fontSize: rem('16px') }}>{currencySymbol}</sup>
              ) : (
                <Text lineHeight="30px" fontWeight={fontWeight}>
                  {currencySymbol}
                </Text>
              )}
            </Text>
          )}
          <Amount textStyle="currency" fontSize={fontSize} lineHeight={lineHeight} textDecoration={textDecoration} data-testid="amount">
            {FormatNumber({ number, decimalPlaces })}
          </Amount>
          {termsConditionsSymbol && (
            <Text data-testid="terms-conditions-symbol" fontSize={fontSize} lineHeight={lineHeight} fontWeight={fontWeight}>
              {alignCurrency === 'superscript' ? (
                <sup style={{ fontSize: rem('20px') }}>{termsConditionsSymbol}</sup>
              ) : (
                termsConditionsSymbol
              )}
            </Text>
          )}
        </Flex>
      )}
      {!hideCurrency && (
        <Box mr={reverse ? 1 : 0} ml={reverse ? 0 : 1}>
          <CurrencyText
            fontWeight="normal"
            textStyle="currency"
            fontSize={currencyTextFontSize}
            lineHeight={lineHeight}
            isPoints={isPoints}
            reverse={reverse}
            data-testid="currency-text-unhidden"
            marginBottom={reverse ? '18px' : '12px'}
          >
            {currency}
            {currencyTextSuffix}
          </CurrencyText>
        </Box>
      )}
    </Wrapper>
  );
};

export default Currency;
