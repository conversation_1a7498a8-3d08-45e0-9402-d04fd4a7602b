import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import PropertyRatings from './PropertyRatings';
import { CustomerRating, RatingType } from 'types/property';
import useRatingTooltip from 'hooks/useRatingTooltip';

interface MockStarProps {
  rating: number;
  size: number;
  ratingType: RatingType;
  tooltip?: object;
}

interface MockTripadvisorRatingProps {
  rating: CustomerRating;
  small: boolean;
  displayReviews: boolean;
  underlined: boolean;
}

jest.mock('@qga/roo-ui/components', () => ({
  Box: ({ children, ...props }: { children: React.ReactNode; [key: string]: unknown }) => <div {...props}>{children}</div>,
  NakedButton: ({ children, onClick }: { children: React.ReactNode; onClick: () => void }) => (
    <button onClick={onClick} data-testid="mocked-naked-button">
      {children}
    </button>
  ),
  StarRating: ({ rating, size, ratingType, tooltip }: MockStarProps) => (
    <div data-testid="mocked-star-rating" data-rating={rating} data-size={size} data-rating-type={ratingType} data-has-tooltip={!!tooltip}>
      Mocked StarRating
    </div>
  ),
}));

jest.mock('components/PropertyRatings/TripAdvisorRating', () => ({
  __esModule: true,
  default: ({ rating, small, displayReviews, underlined }: MockTripadvisorRatingProps) => (
    <div
      data-testid="mocked-tripadvisor-rating"
      data-rating-value={rating?.averageRating}
      data-small={small}
      data-display-reviews={displayReviews}
      data-underlined={underlined}
    >
      Mocked TripAdvisorRating
    </div>
  ),
}));

jest.mock('hooks/useRatingTooltip', () => ({
  __esModule: true,
  default: jest.fn(),
}));

describe('PropertyRatings', () => {
  const mockTooltip = { 'data-testid': 'mock-tooltip' };
  beforeEach(() => {
    (useRatingTooltip as jest.Mock).mockClear();
    (useRatingTooltip as jest.Mock).mockImplementation(({ isEnabled }) => {
      if (isEnabled) {
        return { tooltip: mockTooltip };
      }
      return { tooltip: undefined };
    });
  });

  const baseProps = {
    hotelRating: 4.5,
    hotelRatingType: 'AAA' as const,
    hotelRatingSize: 16,
    customerRating: null,
    showTooltip: true,
    displayTAReviews: false,
    underlineTAReviews: false,
    onTripAdvisorClick: jest.fn(),
  };

  const tripAdvisorRating: CustomerRating = {
    source: 'trip_advisor',
    averageRating: 4.0,
    reviewCount: 100,
    ratingImageUrl: 'http://example.com',
  };

  it('renders StarRating when hotelRating is not zero', () => {
    render(<PropertyRatings {...baseProps} />);
    const starRating = screen.getByTestId('mocked-star-rating');
    expect(starRating).toBeInTheDocument();
    expect(starRating).toHaveAttribute('data-rating', '4.5');
    expect(useRatingTooltip).toHaveBeenCalledWith({
      ratingType: baseProps.hotelRatingType,
      isEnabled: true,
    });
  });

  it('does not render StarRating when hotelRating is zero', () => {
    render(<PropertyRatings {...baseProps} hotelRating={0} />);
    const starRating = screen.queryByTestId('mocked-star-rating');
    expect(starRating).not.toBeInTheDocument();
  });

  it('renders TripAdvisorRating when customerRating source is trip_advisor', () => {
    render(<PropertyRatings {...baseProps} customerRating={tripAdvisorRating} />);
    const taRating = screen.getByTestId('mocked-tripadvisor-rating');
    expect(taRating).toBeInTheDocument();
    expect(taRating).toHaveAttribute('data-rating-value', '4');
  });

  it('does not render TripAdvisorRating when customerRating source is not trip_advisor', () => {
    const customerRating: CustomerRating = {
      source: 'some_other_source',
      averageRating: 4.0,
      reviewCount: 100,
      ratingImageUrl: 'http://example.com',
    };
    render(<PropertyRatings {...baseProps} customerRating={customerRating} />);
    const taRating = screen.queryByTestId('mocked-tripadvisor-rating');
    expect(taRating).not.toBeInTheDocument();
  });

  it('does not render TripAdvisorRating when there are no reviews', () => {
    const noReviewsRating: CustomerRating = { ...tripAdvisorRating, reviewCount: 0 };
    render(<PropertyRatings {...baseProps} customerRating={noReviewsRating} />);
    const taRating = screen.queryByTestId('mocked-tripadvisor-rating');
    expect(taRating).not.toBeInTheDocument();
  });

  it('calls onTripAdvisorClick when the TripAdvisor button is clicked', async () => {
    const onTripAdvisorClickMock = jest.fn();
    render(<PropertyRatings {...baseProps} customerRating={tripAdvisorRating} onTripAdvisorClick={onTripAdvisorClickMock} />);

    const tripAdvisorButton = screen.getByTestId('mocked-naked-button');
    await userEvent.click(tripAdvisorButton);
    expect(onTripAdvisorClickMock).toHaveBeenCalledTimes(1);
  });

  it('passes small prop as true to TripAdvisorRating when hotelRatingSize is less than 16', () => {
    render(<PropertyRatings {...baseProps} customerRating={tripAdvisorRating} hotelRatingSize={15} />);
    const taRating = screen.getByTestId('mocked-tripadvisor-rating');
    expect(taRating).toHaveAttribute('data-small', 'true');
  });

  it('passes small prop as false to TripAdvisorRating when hotelRatingSize is 16 or greater', () => {
    render(<PropertyRatings {...baseProps} customerRating={tripAdvisorRating} hotelRatingSize={16} />);
    const taRating = screen.getByTestId('mocked-tripadvisor-rating');
    expect(taRating).toHaveAttribute('data-small', 'false');
  });

  it('passes displayReviews and underlined props correctly to TripAdvisorRating', () => {
    render(<PropertyRatings {...baseProps} customerRating={tripAdvisorRating} displayTAReviews={true} underlineTAReviews={true} />);
    const taRating = screen.getByTestId('mocked-tripadvisor-rating');
    expect(taRating).toHaveAttribute('data-display-reviews', 'true');
    expect(taRating).toHaveAttribute('data-underlined', 'true');
  });

  it('renders NakedButton when onTripAdvisorClick is provided', () => {
    render(<PropertyRatings {...baseProps} customerRating={tripAdvisorRating} />);
    const nakedButton = screen.getByTestId('mocked-naked-button');
    expect(nakedButton).toBeInTheDocument();
  });

  it('the wrapper is not tabbable when onTripAdvisorClick is not provided', async () => {
    render(<PropertyRatings {...baseProps} hotelRating={0} customerRating={tripAdvisorRating} onTripAdvisorClick={undefined} />);
    const taRatingWrapper = screen.getByTestId('tripadvisor-rating');
    expect(taRatingWrapper).not.toHaveAttribute('tabIndex', '0');
  });

  it('does not render NakedButton when onTripAdvisorClick is not provided', () => {
    render(<PropertyRatings {...baseProps} customerRating={tripAdvisorRating} onTripAdvisorClick={undefined} />);
    const nakedButton = screen.queryByTestId('mocked-naked-button');
    expect(nakedButton).not.toBeInTheDocument();
  });

  it('correctly applies tooltip props from useRatingTooltip when showTooltip is true', () => {
    render(<PropertyRatings {...baseProps} />);
    const starRating = screen.getByTestId('mocked-star-rating');
    expect(starRating).toHaveAttribute('data-has-tooltip', 'true');
    expect(useRatingTooltip).toHaveBeenCalledWith({
      ratingType: baseProps.hotelRatingType,
      isEnabled: true,
    });
  });

  it('does not apply tooltip props when showTooltip is false', () => {
    render(<PropertyRatings {...baseProps} showTooltip={false} />);
    const starRating = screen.getByTestId('mocked-star-rating');
    expect(starRating).toHaveAttribute('data-has-tooltip', 'false');
    expect(useRatingTooltip).toHaveBeenCalledWith({
      ratingType: baseProps.hotelRatingType,
      isEnabled: false,
    });
  });
});
