import React from 'react';
import { Box, NakedButton } from '@qga/roo-ui/components';
import TripAdvisorRating from 'components/PropertyRatings/TripAdvisorRating';
import { CustomerRating, RatingType } from 'types/property';
import HotelRating from './HotelRating';

interface PropertyRatingsProps {
  hotelRating: number;
  hotelRatingType: RatingType;
  hotelRatingSize: number;
  customerRating?: CustomerRating | null;
  showTooltip?: boolean;
  displayTAReviews?: boolean;
  underlineTAReviews?: boolean;
  onTripAdvisorClick?: () => void;
}

const PropertyRatings = ({
  hotelRating,
  hotelRatingType,
  hotelRatingSize,
  customerRating,
  showTooltip = true,
  displayTAReviews = false,
  underlineTAReviews = false,
  onTripAdvisorClick,
}: PropertyRatingsProps) => {
  const notZeroRating = hotelRating !== 0;
  const tripAdvisorLogoSize = hotelRatingSize < 16;
  const hasTripadvisorRatings = customerRating?.source === 'trip_advisor' && customerRating.reviewCount > 0;

  const tripAdvisorRatingComponent = (
    <TripAdvisorRating
      ml={notZeroRating ? 0 : '-6px'}
      rating={customerRating}
      small={tripAdvisorLogoSize}
      displayReviews={displayTAReviews}
      underlined={underlineTAReviews}
    />
  );

  return (
    <>
      {notZeroRating && (
        <HotelRating
          hotelRating={hotelRating}
          hotelRatingType={hotelRatingType}
          hotelRatingSize={hotelRatingSize}
          showTooltip={showTooltip}
        />
      )}
      {hasTripadvisorRatings && (
        <>
          {onTripAdvisorClick ? (
            <NakedButton
              data-testid="tripadvisor-rating"
              onClick={onTripAdvisorClick}
              type="button"
              aria-label={`TripAdvisor rating: ${customerRating.averageRating} out of 5 stars. Based on ${customerRating.reviewCount.toLocaleString()} reviews. Click to see trip advisor reviews`}
            >
              {tripAdvisorRatingComponent}
            </NakedButton>
          ) : (
            <Box data-testid="tripadvisor-rating">{tripAdvisorRatingComponent}</Box>
          )}
        </>
      )}
    </>
  );
};

export default PropertyRatings;
