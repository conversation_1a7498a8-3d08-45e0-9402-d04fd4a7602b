import React from 'react';
import range from 'lodash/range';
import PropTypes from 'prop-types';
import { Flex } from '@qga/roo-ui/components';

export const FullCircle = ({ size }) => (
  <svg data-testid="full-circle" width={size} height={size} viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g id="TA---Filled" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <circle id="Oval" stroke="#00A680" strokeWidth="2" cx="12" cy="12" r="11" />
      <circle id="Oval" fill="#00A680" cx="12" cy="12" r="6" />
    </g>
  </svg>
);

export const HalfCircle = ({ size }) => (
  <svg data-testid="half-circle" width={size} height={size} viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g id="TA---Half" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <circle id="Oval" stroke="#00A680" strokeWidth="2" cx="12" cy="12" r="11" />
      <path d="M12,6 L12,18 C8.6862915,18 6,15.3137085 6,12 C6,8.6862915 8.6862915,6 12,6 Z" id="Combined-Shape" fill="#00A680" />
    </g>
  </svg>
);

export const EmptyCircle = ({ size }) => (
  <svg data-testid="empty-circle" width={size} height={size} viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g id="TA---Empty" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <circle id="Oval" stroke="#00A680" strokeWidth="2" cx="12" cy="12" r="11" />
    </g>
  </svg>
);

const image = (rating, index) => {
  if (index < Number(rating) - 0.5) return FullCircle;
  if (index < Number(rating)) return HalfCircle;
  return EmptyCircle;
};

const ratingText = (rating) => `${rating} out of 5 rating`;

const renderRating = ({ rating, size }) =>
  range(5).map((index) => {
    const TACircle = image(rating, index);
    return <TACircle key={index} role="img" alt={ratingText(rating)} size={size} />;
  });

const RatingCircles = ({ rating, size }) => (
  <Flex itemType="http://schema.org/AggregateRating" aria-label={ratingText(rating)} title={ratingText(rating)}>
    {renderRating({ rating, size })}
  </Flex>
);

const circleProps = {
  size: PropTypes.string.isRequired,
};

FullCircle.propTypes = circleProps;
HalfCircle.propTypes = circleProps;
EmptyCircle.propTypes = circleProps;

RatingCircles.propTypes = {
  rating: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  size: PropTypes.string,
};

RatingCircles.defaultProps = {
  size: '16',
};

export default RatingCircles;
