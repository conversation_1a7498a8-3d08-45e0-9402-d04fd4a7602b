import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import RatingCircles from './RatingCircles';

describe('<RatingCircles />', () => {
  [1, 2, 3, 4].forEach((rating) => {
    it(`renders the ${rating} star rating`, () => {
      render(<RatingCircles rating={rating} size="16" />);

      expect(screen.getAllByTestId('full-circle')).toHaveLength(rating);

      expect(screen.queryAllByTestId('half-circle')).toHaveLength(0);
      expect(screen.queryAllByTestId('empty-circle')).toHaveLength(5 - rating);
    });

    it(`renders the ${rating}.5 star rating`, () => {
      const halfRating = rating + 0.5;
      render(<RatingCircles rating={halfRating} size="16" />);

      expect(screen.getAllByTestId('full-circle')).toHaveLength(Math.floor(halfRating));
      expect(screen.getAllByTestId('half-circle')).toHaveLength(1);
      expect(screen.queryAllByTestId('empty-circle')).toHaveLength(5 - Math.ceil(halfRating));
    });

    it(`renders the 5 star rating`, () => {
      const rating = 5;
      render(<RatingCircles rating={rating} size="16" />);

      expect(screen.getAllByTestId('full-circle')).toHaveLength(rating);

      expect(screen.queryAllByTestId('half-circle')).toHaveLength(0);
      expect(screen.queryAllByTestId('empty-circle')).toHaveLength(0);
    });
  });
});
