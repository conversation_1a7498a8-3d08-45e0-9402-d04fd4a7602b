import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { Flex, Text, Image } from '@qga/roo-ui/components';
import pluralize from 'pluralize';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import formatNumber from 'lib/formatters/formatNumber';
import { TRIP_ADVISOR_RATING_URL } from 'config';
import VisuallyHidden from 'components/VisuallyHidden';

const roundHalf = (number) => Math.round(Number(number) * 2) / 2;

const getRatingUrl = (rating) => {
  const numericRating = Number(rating);
  if (isNaN(numericRating)) {
    return null;
  }
  const ratingString = formatNumber({ number: roundHalf(numericRating), decimalPlaces: 1 });
  return `${TRIP_ADVISOR_RATING_URL}${ratingString}-15969-4.svg`;
};

const ReviewsText = styled(Text)`
  color: ${themeGet('colors.greys.steel')};
  text-decoration: ${(props) => (props.underlined ? 'underline' : 'none')};
  font-size: ${themeGet('fontSizes.xs')};
  margin-left: ${themeGet('space.1')};
`;

const TripAdvisorRating = ({ rating, small = false, displayReviews, underlined, ...rest }) => {
  const { averageRating, reviewCount } = rating || {};

  const imageUrl = useMemo(() => getRatingUrl(averageRating), [averageRating]);

  if (!rating || !reviewCount || !averageRating || !imageUrl) return null;

  const ratingLogoSize = small ? '80px' : '110px';

  const altText = `TripAdvisor rating: ${averageRating} out of 5 stars`;
  const reviewsText = displayReviews ? ` with ${pluralize('reviews', reviewCount, true)}.` : '';

  const descriptiveRatingText = altText + reviewsText;

  return (
    <Flex alignItems="center" flexWrap="wrap" {...rest}>
      <Image src={imageUrl} width={ratingLogoSize} alt="" aria-hidden loading="lazy" decoding="async" />
      <VisuallyHidden data-testid="tripadvisor-rating-description">{descriptiveRatingText}</VisuallyHidden>
      {displayReviews && (
        <>
          <ReviewsText data-testid="reviews-count" underlined={underlined} aria-hidden>
            {pluralize('reviews', reviewCount, true)}
          </ReviewsText>
        </>
      )}
    </Flex>
  );
};

TripAdvisorRating.propTypes = {
  rating: PropTypes.object,
  small: PropTypes.bool,
  displayReviews: PropTypes.bool,
  underlined: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
};

TripAdvisorRating.defaultProps = {
  rating: null,
  small: false,
  displayReviews: false,
  underlined: false,
};

export default TripAdvisorRating;
