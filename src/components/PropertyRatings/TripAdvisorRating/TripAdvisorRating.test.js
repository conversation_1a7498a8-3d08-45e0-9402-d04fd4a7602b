import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import TripAdvisorRating from './TripAdvisorRating';

jest.mock('config', () => ({
  TRIP_ADVISOR_RATING_URL: 'https://www.tripadvisor.com/img/cdsi/img2/ratings/traveler/',
}));

const mockRating = {
  averageRating: 4.2,
  reviewCount: 50,
};

describe('TripAdvisorRating', () => {
  describe('When there is no rating data', () => {
    it('should not render the component when the rating prop is null', () => {
      render(<TripAdvisorRating rating={null} />);
      expect(screen.queryByRole('img', { hidden: true })).not.toBeInTheDocument();
    });

    it('should not render the component when reviewCount is zero', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, reviewCount: 0 }} />);
      expect(screen.queryByRole('img', { hidden: true })).not.toBeInTheDocument();
    });

    it('should not render the component when averageRating is zero', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, averageRating: 0 }} />);
      expect(screen.queryByRole('img', { hidden: true })).not.toBeInTheDocument();
    });

    it('should not render the component when averageRating is invalid', () => {
      render(<TripAdvisorRating rating={{ averageRating: 'invalid', reviewCount: 1 }} />);
      expect(screen.queryByRole('img', { hidden: true })).not.toBeInTheDocument();
    });
  });

  describe('The rating SVG', () => {
    it('should render with the correct src for a whole number rating', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, averageRating: 5, reviewCount: 50 }} />);
      const ratingImage = screen.getByRole('img', {
        alt: 'TripAdvisor rating: 5 out of 5 stars',
        hidden: true,
      });
      expect(ratingImage).toBeInTheDocument();
      expect(ratingImage).toHaveAttribute('src', 'https://www.tripadvisor.com/img/cdsi/img2/ratings/traveler/5.0-15969-4.svg');
    });

    it('should round averageRating 3.8 to 4.0', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, averageRating: 3.8, reviewCount: 50 }} />);
      const ratingImage = screen.getByRole('img', {
        alt: 'TripAdvisor rating: 3.8 out of 5 stars',
        hidden: true,
      });
      expect(ratingImage).toBeInTheDocument();
      expect(ratingImage).toHaveAttribute('src', 'https://www.tripadvisor.com/img/cdsi/img2/ratings/traveler/4.0-15969-4.svg');
    });

    it('should round averageRating 3.2 to 3.0', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, averageRating: 3.2, reviewCount: 50 }} />);
      const ratingImage = screen.getByRole('img', {
        alt: 'TripAdvisor rating: 3.2 out of 5 stars',
        hidden: true,
      });
      expect(ratingImage).toBeInTheDocument();
      expect(ratingImage).toHaveAttribute('src', 'https://www.tripadvisor.com/img/cdsi/img2/ratings/traveler/3.0-15969-4.svg');
    });

    it('should have the correct accessible alt text', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, averageRating: 4.5, reviewCount: 10 }} />);
      const ratingImage = screen.getByRole('img', {
        alt: 'TripAdvisor rating: 4.5 out of 5 stars',
        hidden: true,
      });
      expect(ratingImage).toBeInTheDocument();
    });

    it('should have a default width of 110px', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, averageRating: 4, reviewCount: 50 }} />);
      const ratingImage = screen.getByRole('img', {
        alt: 'TripAdvisor rating: 4 out of 5 stars',
        hidden: true,
      });
      expect(ratingImage).toHaveStyle('width: 110px');
    });

    it('should have a width of 80px when the small prop is true', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, averageRating: 4, reviewCount: 50 }} small />);
      const ratingImage = screen.getByRole('img', {
        alt: 'TripAdvisor rating: 4 out of 5 stars',
        hidden: true,
      });
      expect(ratingImage).toHaveStyle('width: 80px');
    });
  });

  describe('The review count text', () => {
    it('should render the reviews count when displayReviews is true', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, reviewCount: 100 }} displayReviews />);
      expect(screen.getByText('100 reviews')).toBeInTheDocument();
    });

    it('should not render the reviews count when displayReviews is false', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, reviewCount: 100 }} displayReviews={false} />);
      expect(screen.queryByTestId('reviews-count')).not.toBeInTheDocument();
    });

    it('should use pluralize correctly for a single review', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, reviewCount: 1 }} displayReviews />);
      expect(screen.getByText('1 review')).toBeInTheDocument();
    });

    it('should use pluralize correctly for multiple reviews', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, reviewCount: 2 }} displayReviews />);
      expect(screen.getByText('2 reviews')).toBeInTheDocument();
    });

    it('should apply an underline style when the underlined prop is true', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, reviewCount: 100 }} displayReviews underlined />);
      expect(screen.getByText('100 reviews')).toHaveStyle('text-decoration: underline');
    });

    it('should not apply an underline style when the underlined prop is false', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, reviewCount: 100 }} displayReviews underlined={false} />);
      expect(screen.getByText('100 reviews')).toHaveStyle('text-decoration: none');
    });

    it('should not apply an underline style by default', () => {
      render(<TripAdvisorRating rating={{ ...mockRating, reviewCount: 100 }} displayReviews />);
      expect(screen.getByText('100 reviews')).toHaveStyle('text-decoration: none');
    });
  });

  describe('Accessibility', () => {
    it('should have a visually hidden span with descriptive text for screen readers when displayReviews is true', () => {
      render(<TripAdvisorRating rating={mockRating} displayReviews />);
      const hiddenText = screen.getByText('TripAdvisor rating: 4.2 out of 5 stars with 50 reviews.');
      expect(hiddenText).toBeInTheDocument();
      expect(hiddenText).toHaveStyle(`
        clip: rect(0 0 0 0);
        clip-path: inset(50%);
        height: 1px;
        overflow: hidden;
        position: absolute;
        white-space: nowrap;
        width: 1px;
      `);

      const ratingImage = screen.getByRole('img', { hidden: true });
      expect(ratingImage).toHaveAttribute('aria-hidden', 'true');
      const reviewsText = screen.getByTestId('reviews-count');
      expect(reviewsText).toHaveAttribute('aria-hidden', 'true');
    });
  });
});
