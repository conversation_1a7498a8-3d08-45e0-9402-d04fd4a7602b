import React from 'react';
import { render, screen } from '@testing-library/react';
import HotelRating from './HotelRating';
import { RatingType } from 'types/property';
import { ACCESSIBILITY_RATING_TOOLTIP_MESSAGES } from 'config';
import { StarRatingProps } from '@qga/roo-ui/components';

jest.mock('@qga/roo-ui/components', () => ({
  Box: ({ children }: { children: React.ReactNode }) => <div data-testid="mock-box">{children}</div>,
  StarRating: ({ rating, ratingType, size, excludeTitle, ...rest }: StarRatingProps) => (
    <div
      data-testid="mock-star-rating"
      data-rating={rating}
      data-rating-type={ratingType}
      data-size={size}
      data-exclude-title={excludeTitle}
      {...rest}
    >
      Mock Star Rating
    </div>
  ),
}));

jest.mock('components/VisuallyHidden', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="mock-visually-hidden">{children}</div>,
}));

jest.mock('hooks/useRatingTooltip', () => ({
  __esModule: true,
  default: jest.fn(({ isEnabled }) => {
    if (isEnabled) {
      return {
        'data-tooltip-content': 'mock-tooltip-content',
        'data-tooltip-id': 'mock-tooltip-id',
      };
    }
    return {};
  }),
}));

describe('HotelRating', () => {
  const defaultProps = {
    hotelRating: 4,
    hotelRatingType: 'AAA' as RatingType,
    hotelRatingSize: 20,
    showTooltip: false,
  };

  it('renders the correct star rating and passes the correct props', () => {
    render(<HotelRating {...defaultProps} />);
    const starRating = screen.getByTestId('mock-star-rating');
    expect(starRating).toBeInTheDocument();
    expect(starRating).toHaveAttribute('data-rating', '4');
    expect(starRating).toHaveAttribute('data-rating-type', 'AAA');
    expect(starRating).toHaveAttribute('data-size', '20');
    expect(starRating).toHaveAttribute('data-exclude-title', 'true');
  });

  it('renders the correct description for screen readers', () => {
    render(<HotelRating {...defaultProps} />);
    const visuallyHidden = screen.getByTestId('mock-visually-hidden');

    const expectedDescription = `Rating: 4 out of 5 ${ACCESSIBILITY_RATING_TOOLTIP_MESSAGES['AAA']}`;
    expect(visuallyHidden).toHaveTextContent(expectedDescription);
  });

  it('does not pass tooltip props when showTooltip is false', () => {
    render(<HotelRating {...defaultProps} showTooltip={false} />);
    const starRating = screen.getByTestId('mock-star-rating');

    expect(starRating).not.toHaveAttribute('data-tooltip-content');
    expect(starRating).not.toHaveAttribute('data-tooltip-id');
  });

  it('passes tooltip props when showTooltip is true', () => {
    const propsWithTooltip = {
      ...defaultProps,
      showTooltip: true,
    };
    render(<HotelRating {...propsWithTooltip} />);
    const starRating = screen.getByTestId('mock-star-rating');

    expect(starRating).toHaveAttribute('data-tooltip-content', 'mock-tooltip-content');
    expect(starRating).toHaveAttribute('data-tooltip-id', 'mock-tooltip-id');
  });

  it('renders correctly with different rating types', () => {
    const guestHouseProps = {
      ...defaultProps,
      hotelRatingType: 'SELF_RATED' as RatingType,
    };

    render(<HotelRating {...guestHouseProps} />);

    const visuallyHidden = screen.getByTestId('mock-visually-hidden');
    const expectedDescription = `Rating: 4 out of 5 ${ACCESSIBILITY_RATING_TOOLTIP_MESSAGES['SELF_RATED']}`;
    expect(visuallyHidden).toHaveTextContent(expectedDescription);
  });
});
