import React from 'react';
import { Box, StarRating } from '@qga/roo-ui/components';
import { RatingType } from 'types/property';
import useRatingTooltip from 'hooks/useRatingTooltip';
import VisuallyHidden from 'components/VisuallyHidden';
import { ACCESSIBILITY_RATING_TOOLTIP_MESSAGES } from 'config';

interface HotelRatingProps {
  hotelRating: number;
  hotelRatingType: RatingType;
  hotelRatingSize: number;
  showTooltip?: boolean;
}

const HotelRating = ({ hotelRating, hotelRatingType, hotelRatingSize, showTooltip }: HotelRatingProps) => {
  const ratingTooltip = useRatingTooltip({ ratingType: hotelRatingType, isEnabled: showTooltip });
  const hotelRatingDescription = `Rating: ${hotelRating} out of 5 ${ACCESSIBILITY_RATING_TOOLTIP_MESSAGES[hotelRatingType]}`;

  return (
    <>
      <VisuallyHidden data-testid="hotel-rating-description">{hotelRatingDescription}</VisuallyHidden>
      <Box mr={2} data-testid="star-rating" aria-hidden>
        <StarRating rating={hotelRating} ratingType={hotelRatingType} size={hotelRatingSize} {...ratingTooltip} excludeTitle />
      </Box>
    </>
  );
};

export default HotelRating;
