import React from 'react';
import range from 'lodash/range';
import PropTypes from 'prop-types';
import { Box } from '@qga/roo-ui/components';

const SkeletonResults = React.memo(({ count, skeletonCardComponent: SkeletonCard }) => {
  return (
    <Box data-testid="skeleton-loader">
      {range(count).map((index) => (
        <SkeletonCard key={index} />
      ))}
    </Box>
  );
});

SkeletonResults.displayName = 'SkeletonResults';

SkeletonResults.propTypes = {
  count: PropTypes.number.isRequired,
  skeletonCardComponent: PropTypes.elementType.isRequired,
};

export default SkeletonResults;
