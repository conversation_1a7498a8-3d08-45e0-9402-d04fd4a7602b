import React from 'react';
import { Box, Text } from '@qga/roo-ui/components';
import Currency from 'components/Currency';
import PointsStrikethroughMessage from 'components/PointsStrikethroughMessage';

interface PriceBeforeDiscountProps {
  total: { amount: string; currency: string };
  discount: { amount: number; currency: string };
  roundToCeiling?: boolean;
  lineHeight?: number | string;
  offerType?: string | undefined;
  [key: string]: unknown;
  showMessage?: boolean;
}

const PriceBeforeDiscount: React.FC<PriceBeforeDiscountProps> = (props) => {
  const { total, discount, roundToCeiling = false, lineHeight = 1, showMessage = true, ...restProps } = props;

  if (!total.amount) {
    return null;
  }

  const textProps = {
    color: 'greys.steel',
    fontSize: 'sm',
    lineHeight,
  };

  return (
    <Box style={{ lineHeight }} data-testid="price-before-discount">
      <Currency
        amount={total.amount}
        currency={total.currency}
        roundToCeiling={roundToCeiling}
        {...textProps}
        {...restProps}
        textDecoration="line-through"
        data-testid="currency-before-discount"
        hideCurrency={true}
      />
      <Text {...textProps} textDecoration="line-through">
        {' '}
        PTS
      </Text>
      {showMessage && <PointsStrikethroughMessage {...textProps} discount={discount} />}
    </Box>
  );
};

export default PriceBeforeDiscount;
