import React from 'react';
import { screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import PriceBeforeDiscount from './PriceBeforeDiscount';
import { renderWithProviders } from 'test-utils/reactUtils';

jest.mock('components/Currency', () => {
  return ({ amount, currency, roundToCeiling, hideCurrency, size }) => (
    <span data-testid="currency-component">
      {hideCurrency ? '' : currency} {amount} {roundToCeiling ? 'rounded' : ''} {size}
    </span>
  );
});

jest.mock('components/PointsStrikethroughMessage', () => {
  return ({ discount }) => {
    const safeDiscount = discount || { amount: '', currency: '' };
    return <div data-testid="points-strikethrough-message">`Save ({safeDiscount.amount})`</div>;
  };
});

describe('<PriceBeforeDiscount />', () => {
  let props;
  const total = {
    amount: '2000',
    currency: 'PTS',
  };
  const discount = {
    amount: '500',
    currency: 'PTS',
  };

  beforeEach(() => {
    props = {
      total: total,
      discount: discount,
      roundToCeiling: true,
      hideCurrency: true,
      size: 'xl',
      offerType: 'NON classic',
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('when a total amount exists', () => {
    it('renders the Currency component with the total price', () => {
      renderWithProviders(<PriceBeforeDiscount {...props} />);
      expect(screen.getByTestId('currency-component')).toHaveTextContent('2000');
      expect(screen.getByTestId('currency-component')).toHaveTextContent('xl');
      expect(screen.getByTestId('currency-component')).toHaveTextContent('rounded');
      expect(screen.getByTestId('currency-component')).not.toHaveTextContent('PTS');
    });
  });

  describe('when total amount is 0', () => {
    it('does not render the Currency component', () => {
      renderWithProviders(<PriceBeforeDiscount {...props} total={{ amount: 0, currency: 'PTS' }} />);
      expect(screen.queryByTestId('currency-component')).not.toBeInTheDocument();
    });
  });

  describe('when total amount is null', () => {
    it('does not render the Currency component', () => {
      renderWithProviders(<PriceBeforeDiscount {...props} total={{ amount: null, currency: 'PTS' }} />);
      expect(screen.queryByTestId('currency-component')).not.toBeInTheDocument();
    });
  });

  describe('points strike-through', () => {
    describe('when showMessage is false', () => {
      it('does not render the strike through message', () => {
        renderWithProviders(<PriceBeforeDiscount {...props} showMessage={false} />);
        expect(screen.queryByTestId('points-strikethrough-message')).not.toBeInTheDocument();
      });
    });

    describe('when showMessage is true', () => {
      it('renders the strike through message', () => {
        renderWithProviders(<PriceBeforeDiscount {...props} showMessage={true} />);
        expect(screen.getByTestId('points-strikethrough-message')).toBeInTheDocument();
        expect(screen.getByTestId('points-strikethrough-message')).toHaveTextContent('Save (500)');
      });
    });
  });
});
