import React from 'react';
import { screen } from '@testing-library/react';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import PayWithToggleMessage from './PayWithToggleMessage';
import { getPayWithToggleMessage } from 'store/campaign/campaignSelectors';
import { getIsExclusive } from 'store/property/propertySelectors';
import usePointsRedemptionStrikethrough from 'hooks/optimizely/usePointsRedemptionStrikethrough/usePointsRedemptionStrikethrough';

jest.mock('store/campaign/campaignSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('hooks/optimizely/usePointsRedemptionStrikethrough/usePointsRedemptionStrikethrough');

const props = { pointerDirection: 'left', pointerPosition: '50%' };

const render = () => renderWithProviders(<PayWithToggleMessage {...props} />);

describe('<PayWithToggleMessage />', () => {
  const message = 'message';

  beforeEach(() => {
    getPayWithToggleMessage.mockReturnValue(message);
    getIsExclusive.mockReturnValue(false);
    usePointsRedemptionStrikethrough.mockReturnValue({
      isReady: true,
      isPointsRedemptionStrikethrough: false,
      tooltipMessage: '',
    });
  });

  it('renders the message', () => {
    render();
    expect(screen.getByText(message)).toBeInTheDocument();
  });

  describe('when points redemption strikethrough is enabled and has the tooltip value', () => {
    const tooltipMessage = 'tooltip message';
    beforeEach(() => {
      usePointsRedemptionStrikethrough.mockReturnValue({
        isReady: true,
        isPointsRedemptionStrikethrough: true,
        tooltipMessage: tooltipMessage,
      });
    });

    it('renders the tooltip message instead of the pay with toggle message', () => {
      render();
      expect(screen.getByText(tooltipMessage)).toBeInTheDocument();
    });
  });

  describe('without a message', () => {
    it('renders nothing when message is null', () => {
      getPayWithToggleMessage.mockReturnValue(null);
      render();
      expect(screen.queryByText(message)).not.toBeInTheDocument();
    });

    it('renders nothing when message is undefined', () => {
      getPayWithToggleMessage.mockReturnValue(undefined);
      render();
      expect(screen.queryByText(message)).not.toBeInTheDocument();
    });

    it('renders nothing when message is empty string', () => {
      getPayWithToggleMessage.mockReturnValue('');
      render();
      expect(screen.queryByText(message)).not.toBeInTheDocument();
    });

    it('renders nothing when message is false', () => {
      getPayWithToggleMessage.mockReturnValue(false);
      render();
      expect(screen.queryByText(message)).not.toBeInTheDocument();
    });
  });

  describe('when property is exclusive', () => {
    it('renders nothing even with valid message', () => {
      getIsExclusive.mockReturnValue(true);
      render();
      expect(screen.queryByText(message)).not.toBeInTheDocument();
    });

    it('renders nothing when both exclusive and no message', () => {
      getIsExclusive.mockReturnValue(true);
      getPayWithToggleMessage.mockReturnValue(null);
      render();
      expect(screen.queryByText(message)).not.toBeInTheDocument();
    });
  });

  describe('prop handling', () => {
    it.each(['top', 'bottom', 'left', 'right'])('renders with pointer direction %s', (direction) => {
      render({ pointerDirection: direction });
      expect(screen.getByText(message)).toBeInTheDocument();
    });

    it('renders with custom pointer position', () => {
      render({ pointerPosition: '25%' });
      expect(screen.getByText(message)).toBeInTheDocument();
    });
  });
});
