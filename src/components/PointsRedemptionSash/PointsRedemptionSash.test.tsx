import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import PointRedemptionSash from './PointsRedemptionSash';

describe('<PointRedemptionSash />', () => {
  describe('when campaignMessage prop is received', () => {
    it('renders the points redemption sash', () => {
      const view = render(<PointRedemptionSash campaignMessage="Save 10,000 PTS" backgroundColor="green" color="white" />);
      expect(screen.getByText('Save 10,000 PTS')).toBeInTheDocument();
      expect(view).toMatchSnapshot();
    });
  });
});
