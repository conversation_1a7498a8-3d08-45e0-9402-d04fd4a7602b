import React from 'react';
import { Flex, FlexProps, Icon, Text } from '@qga/roo-ui/components';
import { POINTS_EARN_ENABLED } from 'config';
import styled from '@emotion/styled';
import formatNumber from 'lib/formatters/formatNumber';

interface PointsEarnSummaryProps extends FlexProps {
  total: number;
  fontSize?: string[] | string;
  luxuryInclusions?: boolean;
  topMargin?: number;
}

const PointsBox = styled(Flex)`
  justify-content: start;
  flex-direction: row;
  line-height: tight;
`;

const PointsEarnSummary = ({ total, fontSize = 'sm', luxuryInclusions = false, topMargin = 0, ...rest }: PointsEarnSummaryProps) => {
  if (!POINTS_EARN_ENABLED || total <= 0) {
    return null;
  }
  const formattedTotal = formatNumber({ number: total, decimalPlaces: 0 });

  return (
    <PointsBox mt={topMargin} {...rest}>
      {luxuryInclusions && <Icon name="roo" mr="4px" mt="3px" color="brand.primary" size={20} />}
      <Text data-testid="points-earn-summary-text" color="greys.charcoal" fontSize={fontSize} textAlign="end">
        Earn <Text data-testid="total-points-displayed">{formattedTotal}</Text> PTS^
      </Text>
    </PointsBox>
  );
};

export default PointsEarnSummary;
