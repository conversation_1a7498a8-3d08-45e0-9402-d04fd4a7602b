import React from 'react';
import { screen } from '@testing-library/react';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import PointsEarnSummary from './PointsEarnSummary';
import * as config from 'config';

jest.mock('config');

const render = (props) => renderWithProviders(<PointsEarnSummary {...props} />);

beforeEach(() => {
  jest.clearAllMocks();
  Object.assign(config, jest.requireActual('config'));
});

describe('when POINTS_EARN_ENABLED flag is on', () => {
  beforeEach(() => {
    Object.assign(config, { POINTS_EARN_ENABLED: true });
  });

  describe('when total points is provided', () => {
    it('return the correct text', () => {
      render({ total: 1200 });
      expect(screen.getByTestId('points-earn-summary-text')).toHaveTextContent('Earn 1,200 PTS^');
    });

    it('does not render Icon when luxuryInclusions is false', () => {
      render({ total: 1200, luxuryInclusions: false });
      expect(screen.queryByRole('img')).not.toBeInTheDocument();
    });
  });

  describe('with no total points', () => {
    it('returns no text when total is 0', () => {
      render({ total: 0 });
      expect(screen.queryByText(/earn/i)).not.toBeInTheDocument();
    });

    it('returns no text when total is negative', () => {
      render({ total: -100 });
      expect(screen.queryByText(/earn/i)).not.toBeInTheDocument();
    });
  });

  describe('with luxury inclusions', () => {
    it('renders Icon when luxuryInclusions is true', () => {
      render({ total: 1200, luxuryInclusions: true });
      expect(screen.getByTitle('roo')).toBeInTheDocument();
    });

    it('renders both Icon and text when luxuryInclusions is true', () => {
      render({ total: 1200, luxuryInclusions: true });
      expect(screen.getByTestId('points-earn-summary-text')).toHaveTextContent('Earn 1,200 PTS^');
    });
  });
});

describe('when POINTS_EARN_ENABLED flag is off', () => {
  beforeEach(() => {
    Object.assign(config, { POINTS_EARN_ENABLED: false });
  });

  it('returns no text when flag is disabled', () => {
    render({ total: 1200 });
    expect(screen.queryByText(/earn/i)).not.toBeInTheDocument();
  });

  it('does not render Icon even with luxuryInclusions when flag is disabled', () => {
    render({ total: 1200, luxuryInclusions: true });
    expect(screen.queryByRole('img')).not.toBeInTheDocument();
  });

  it('returns null component when flag is disabled', () => {
    render({ total: 1200 });
    expect(screen.queryByTestId('points-earn-summary-text')).not.toBeInTheDocument();
  });
});
